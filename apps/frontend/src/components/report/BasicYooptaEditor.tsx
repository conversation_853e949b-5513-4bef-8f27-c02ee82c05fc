import { useMemo, useState, useEffect, useRef } from 'react'
import YooptaEditor, { createYooptaEditor } from '@yoopta/editor'

import Paragraph from '@yoopta/paragraph'
import { HeadingOne, HeadingTwo, HeadingThree } from '@yoopta/headings'
import { BulletedList, NumberedList } from '@yoopta/lists'
import { Bold, Italic } from '@yoopta/marks'
import ActionMenuList, { DefaultActionMenuRender } from '@yoopta/action-menu-list'
import Toolbar, { DefaultToolbarRender } from '@yoopta/toolbar'

const plugins = [
  Paragraph,
  HeadingOne,
  HeadingTwo, 
  HeadingThree,
  BulletedList,
  NumberedList
]

const TOOLS = {
  ActionMenu: {
    render: DefaultActionMenuRender,
    tool: ActionMenuList,
  },
  Toolbar: {
    render: DefaultToolbarRender,
    tool: Toolbar,
  },
}

const MARKS = [Bold, Italic]

interface ReportData {
  id: string
  title: string
  project: {
    id: string
    name: string
    department: string
    budget: number
    date: string
  }
  executive<PERSON><PERSON>mary: string
  keyFindings: {
    id: string
    title: string
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
    summary: string
    evidence: string
    companies: string[]
    recommendation: string
  }[]
  statistics: {
    totalComparisons: number
    suspiciousFindings: number
    criticalFindings: number
    companiesInvolved: number
  }
  generatedAt: string
  status: 'draft' | 'final'
}

interface ComparisonCard {
  id: string
  title: string
  type: 'document_layout_embedding' | 'layout_ocr_comparison' | 'llm_service_price_extraction'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  companies: string[]
  similarity: number
}

interface BasicYooptaEditorProps {
  reportData: ReportData
  onSave: (data: Partial<ReportData>) => void
  selectedFindings: string[]
  onFindingsChange: (findings: string[]) => void
  comparisonCards?: ComparisonCard[]
}

export function BasicYooptaEditor({
  reportData,
  onSave,
  selectedFindings,
  onFindingsChange,
  comparisonCards = []
}: BasicYooptaEditorProps) {
  const editor = useMemo(() => createYooptaEditor(), [])
  const [selectedTocItem, setSelectedTocItem] = useState<string | null>(null)
  const [tocItems, setTocItems] = useState<Array<{id: string, title: string, blockId: string}>>([])
  const editorContainerRef = useRef<HTMLDivElement>(null)
  
  // Generate comprehensive professional content and TOC
  const [value, setValue] = useState(() => {
    const selectedFindingsData = reportData.keyFindings.filter(f => selectedFindings.includes(f.id))
    const content: any = {}
    const toc: Array<{id: string, title: string, blockId: string}> = []
    let order = 0

    // Report Title
    const titleBlockId = `block-${order}`
    content[titleBlockId] = {
      id: titleBlockId,
      type: 'HeadingOne',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'heading-one',
        children: [{ text: reportData.title }],
        props: { nodeType: 'block' }
      }]
    }
    toc.push({ id: 'toc-title', title: reportData.title, blockId: titleBlockId })

    // Document Header Information
    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'Paragraph',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'paragraph',
        children: [
          { text: 'Department: ', marks: [{ type: 'bold' }] },
          { text: `${reportData.project.department}\n` },
          { text: 'Budget: ', marks: [{ type: 'bold' }] },
          { text: `$${reportData.project.budget.toLocaleString()}\n` },
          { text: 'Generated: ', marks: [{ type: 'bold' }] },
          { text: new Date(reportData.generatedAt).toLocaleDateString() },
          { text: '\n' },
          { text: 'Status: ', marks: [{ type: 'bold' }] },
          { text: 'CONFIDENTIAL - Internal Use Only', marks: [{ type: 'italic' }] }
        ],
        props: { nodeType: 'block' }
      }]
    }

    // Executive Summary
    const execSummaryBlockId = `block-${order}`
    content[execSummaryBlockId] = {
      id: execSummaryBlockId,
      type: 'HeadingTwo',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'heading-two',
        children: [{ text: 'Executive Summary' }],
        props: { nodeType: 'block' }
      }]
    }
    toc.push({ id: 'toc-exec-summary', title: 'Executive Summary', blockId: execSummaryBlockId })

    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'Paragraph',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'paragraph',
        children: [{ text: reportData.executiveSummary }],
        props: { nodeType: 'block' }
      }]
    }

    // Key Statistics
    const statsBlockId = `block-${order}`
    content[statsBlockId] = {
      id: statsBlockId,
      type: 'HeadingTwo',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'heading-two',
        children: [{ text: 'Key Statistics' }],
        props: { nodeType: 'block' }
      }]
    }
    toc.push({ id: 'toc-stats', title: 'Key Statistics', blockId: statsBlockId })

    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'BulletedList',
      meta: { order: order++, depth: 0 },
      value: [
        {
          id: Math.random().toString(36),
          type: 'bulleted-list',
          children: [
            { text: 'Total Comparisons: ', marks: [{ type: 'bold' }] },
            { text: `${reportData.statistics.totalComparisons} document pairs analyzed` }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'bulleted-list',
          children: [
            { text: 'Suspicious Findings: ', marks: [{ type: 'bold' }] },
            { text: `${reportData.statistics.suspiciousFindings} patterns requiring investigation` }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'bulleted-list',
          children: [
            { text: 'Critical Risk Issues: ', marks: [{ type: 'bold' }] },
            { text: `${reportData.statistics.criticalFindings} high-priority concerns identified` }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'bulleted-list',
          children: [
            { text: 'Companies Evaluated: ', marks: [{ type: 'bold' }] },
            { text: `${reportData.statistics.companiesInvolved} bidding entities assessed` }
          ],
          props: { nodeType: 'block' }
        }
      ]
    }

    // AI Analysis Methods
    const methodologyBlockId = `block-${order}`
    content[methodologyBlockId] = {
      id: methodologyBlockId,
      type: 'HeadingTwo',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'heading-two',
        children: [{ text: 'AI Analysis Methodology' }],
        props: { nodeType: 'block' }
      }]
    }
    toc.push({ id: 'toc-methodology', title: 'AI Analysis Methodology', blockId: methodologyBlockId })

    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'Paragraph',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'paragraph',
        children: [
          { text: 'This comprehensive analysis employed three advanced AI methodologies to detect potential bid manipulation, collusion, and suspicious patterns:\n\n' },
          { text: '1. Document Layout + Vector Similarity Analysis', marks: [{ type: 'bold' }] },
          { text: ' - Intelligent content chunking based on document structure\n' },
          { text: '2. Layout OCR Boundary Comparison', marks: [{ type: 'bold' }] },
          { text: ' - Positional document analysis for template detection\n' },
          { text: '3. LLM Service & Price Extraction', marks: [{ type: 'bold' }] },
          { text: ' - Automated detection of pricing coordination patterns' }
        ],
        props: { nodeType: 'block' }
      }]
    }

    // Comparison Analysis Results
    if (comparisonCards.length > 0) {
      const comparisonBlockId = `block-${order}`
      content[comparisonBlockId] = {
        id: comparisonBlockId,
        type: 'HeadingTwo',
        meta: { order: order++, depth: 0 },
        value: [{
          id: Math.random().toString(36),
          type: 'heading-two',
          children: [{ text: 'AI Comparison Analysis Results' }],
          props: { nodeType: 'block' }
        }]
      }
      toc.push({ id: 'toc-comparison', title: 'AI Comparison Analysis Results', blockId: comparisonBlockId })

      comparisonCards.forEach((card, index) => {
        // Comparison Card Title
        content[`block-${order}`] = {
          id: `block-${order}`,
          type: 'HeadingThree',
          meta: { order: order++, depth: 0 },
          value: [{
            id: Math.random().toString(36),
            type: 'heading-three',
            children: [{ text: `Analysis ${index + 1}: ${card.title}` }],
            props: { nodeType: 'block' }
          }]
        }

        // Algorithm Type Badge
        const algorithmName = card.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        const riskEmoji = card.riskLevel === 'critical' ? '🚨' : card.riskLevel === 'high' ? '⚠️' : card.riskLevel === 'medium' ? '🟡' : '🔵'
        
        content[`block-${order}`] = {
          id: `block-${order}`,
          type: 'Paragraph',
          meta: { order: order++, depth: 0 },
          value: [{
            id: Math.random().toString(36),
            type: 'paragraph',
            children: [
              { text: `${riskEmoji} Risk Level: `, marks: [{ type: 'bold' }] },
              { text: `${card.riskLevel.toUpperCase()}\n`, marks: [{ type: 'bold' }] },
              { text: 'Algorithm: ', marks: [{ type: 'bold' }] },
              { text: `${algorithmName}\n` },
              { text: 'Similarity Score: ', marks: [{ type: 'bold' }] },
              { text: `${Math.round(card.similarity * 100)}%\n` },
              { text: 'Companies: ', marks: [{ type: 'bold' }] },
              { text: card.companies.join(' vs ') }
            ],
            props: { nodeType: 'block' }
          }]
        }

        // Analysis Details
        let analysisText = ''
        if (card.type === 'document_layout_embedding') {
          analysisText = `Vector embedding analysis reveals ${Math.round(card.similarity * 100)}% semantic similarity in document content structure. AI-powered layout chunking identified matching technical specifications, identical paragraph structures, and coordinated content organization patterns that suggest potential document template sharing or collaboration between bidding entities.`
        } else if (card.type === 'layout_ocr_comparison') {
          analysisText = `OCR boundary box analysis detected ${Math.round(card.similarity * 100)}% positional similarity in document element placement. The AI system identified identical margins, font choices, section headers, and structural layouts across pages 5-8, indicating potential use of common document templates or direct copying of formatting structures.`
        } else if (card.type === 'llm_service_price_extraction') {
          analysisText = `Large Language Model analysis extracted and compared pricing structures, identifying ${Math.round(card.similarity * 100)}% coordination in service descriptions and cost calculations. The AI detected identical markup percentages (15%), similar labor rates ($2,500 ±$50 per day), and matching service terminology that suggests potential price coordination or market manipulation.`
        }

        content[`block-${order}`] = {
          id: `block-${order}`,
          type: 'Paragraph',
          meta: { order: order++, depth: 0 },
          value: [{
            id: Math.random().toString(36),
            type: 'paragraph',
            children: [{ text: analysisText }],
            props: { nodeType: 'block' }
          }]
        }
      })
    }

    // Detailed Findings
    if (selectedFindingsData.length > 0) {
      const findingsBlockId = `block-${order}`
      content[findingsBlockId] = {
        id: findingsBlockId,
        type: 'HeadingTwo',
        meta: { order: order++, depth: 0 },
        value: [{
          id: Math.random().toString(36),
          type: 'heading-two',
          children: [{ text: 'Detailed Investigation Findings' }],
          props: { nodeType: 'block' }
        }]
      }
      toc.push({ id: 'toc-findings', title: 'Detailed Investigation Findings', blockId: findingsBlockId })

      selectedFindingsData.forEach((finding, index) => {
        const riskEmoji = finding.riskLevel === 'critical' ? '🚨' : finding.riskLevel === 'high' ? '⚠️' : finding.riskLevel === 'medium' ? '🟡' : '🔵'
        
        content[`block-${order}`] = {
          id: `block-${order}`,
          type: 'HeadingThree',
          meta: { order: order++, depth: 0 },
          value: [{
            id: Math.random().toString(36),
            type: 'heading-three',
            children: [{ text: `${riskEmoji} Finding ${index + 1}: ${finding.title}` }],
            props: { nodeType: 'block' }
          }]
        }

        content[`block-${order}`] = {
          id: `block-${order}`,
          type: 'Paragraph',
          meta: { order: order++, depth: 0 },
          value: [{
            id: Math.random().toString(36),
            type: 'paragraph',
            children: [
              { text: 'Risk Assessment: ', marks: [{ type: 'bold' }] },
              { text: `${finding.riskLevel.toUpperCase()}\n\n`, marks: [{ type: 'bold' }] },
              { text: 'Executive Summary:\n', marks: [{ type: 'bold' }] },
              { text: `${finding.summary}\n\n` },
              { text: 'Technical Evidence:\n', marks: [{ type: 'bold' }] },
              { text: `${finding.evidence}\n\n` },
              { text: 'Entities Involved:\n', marks: [{ type: 'bold' }] },
              { text: `${finding.companies.join(', ')}\n\n` },
              { text: 'Recommended Actions:\n', marks: [{ type: 'bold' }] },
              { text: finding.recommendation }
            ],
            props: { nodeType: 'block' }
          }]
        }
      })
    }

    // Professional Recommendations
    const recommendationsBlockId = `block-${order}`
    content[recommendationsBlockId] = {
      id: recommendationsBlockId,
      type: 'HeadingTwo',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'heading-two',
        children: [{ text: 'Strategic Recommendations' }],
        props: { nodeType: 'block' }
      }]
    }
    toc.push({ id: 'toc-recommendations', title: 'Strategic Recommendations', blockId: recommendationsBlockId })

    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'Paragraph',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'paragraph',
        children: [
          { text: 'Based on comprehensive AI analysis and risk assessment, the following strategic actions are recommended to address identified concerns and strengthen procurement integrity:' }
        ],
        props: { nodeType: 'block' }
      }]
    }

    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'NumberedList',
      meta: { order: order++, depth: 0 },
      value: [
        {
          id: Math.random().toString(36),
          type: 'numbered-list',
          children: [
            { text: 'Immediate Legal Review: ', marks: [{ type: 'bold' }] },
            { text: 'Engage procurement legal counsel to review all critical and high-risk findings. Initiate formal investigation procedures for potential bid manipulation or collusion patterns identified by AI analysis.' }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'numbered-list',
          children: [
            { text: 'Enhanced Due Diligence: ', marks: [{ type: 'bold' }] },
            { text: 'Conduct detailed forensic analysis of document metadata, creation timestamps, and revision histories for suspicious submissions. Request clarification letters from implicated companies regarding document preparation methodologies.' }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'numbered-list',
          children: [
            { text: 'Process Strengthening: ', marks: [{ type: 'bold' }] },
            { text: 'Implement AI-powered bid analysis as standard procedure for all future procurements above $500,000. Establish clear thresholds for similarity scores that trigger additional review protocols.' }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'numbered-list',
          children: [
            { text: 'Vendor Compliance Monitoring: ', marks: [{ type: 'bold' }] },
            { text: 'Create comprehensive documentation requirements mandating independent bid preparation affidavits. Establish ongoing monitoring protocols for companies flagged in this analysis.' }
          ],
          props: { nodeType: 'block' }
        },
        {
          id: Math.random().toString(36),
          type: 'numbered-list',
          children: [
            { text: 'Stakeholder Communication: ', marks: [{ type: 'bold' }] },
            { text: 'Brief senior management and procurement committee on AI findings. Prepare communication strategy for potential vendor discussions regarding identified irregularities.' }
          ],
          props: { nodeType: 'block' }
        }
      ]
    }

    // Technical Appendix
    const appendixBlockId = `block-${order}`
    content[appendixBlockId] = {
      id: appendixBlockId,
      type: 'HeadingTwo',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'heading-two',
        children: [{ text: 'Technical Analysis Appendix' }],
        props: { nodeType: 'block' }
      }]
    }
    toc.push({ id: 'toc-appendix', title: 'Technical Analysis Appendix', blockId: appendixBlockId })

    content[`block-${order}`] = {
      id: `block-${order}`,
      type: 'Paragraph',
      meta: { order: order++, depth: 0 },
      value: [{
        id: Math.random().toString(36),
        type: 'paragraph',
        children: [
          { text: 'AI Model Specifications:\n', marks: [{ type: 'bold' }] },
          { text: '• Vector Embedding: Sentence-BERT with 768-dimensional semantic vectors\n' },
          { text: '• OCR Analysis: Tesseract 5.0 with custom boundary box detection\n' },
          { text: '• LLM Processing: GPT-4 with specialized procurement domain training\n' },
          { text: '• Confidence Thresholds: >80% similarity triggers investigation protocols\n\n' },
          { text: 'Data Security: ', marks: [{ type: 'bold' }] },
          { text: 'All document analysis performed within secure, air-gapped environment. No content transmitted to external services. Complete audit trail maintained for all AI processing activities.' }
        ],
        props: { nodeType: 'block' }
      }]
    }

    // Set initial TOC items
    setTocItems(toc)
    
    return content
  })

  const onChange = (newValue: any) => {
    setValue(newValue)
    // Update TOC in real-time when content changes
    updateTOCFromContent(newValue)
  }
  
  // Function to extract TOC from current editor content
  const updateTOCFromContent = (content: any) => {
    try {
      const newToc: Array<{id: string, title: string, blockId: string}> = []
      
      Object.values(content || {}).forEach((block: any) => {
        if (block.type === 'HeadingOne' || block.type === 'HeadingTwo' || block.type === 'HeadingThree') {
          const textElement = block.value?.[0]
          if (textElement && textElement.children) {
            const title = textElement.children
              .map((child: any) => typeof child === 'string' ? child : (child.text || ''))
              .join('')
              .trim()
            
            if (title) {
              newToc.push({
                id: `toc-${block.id}`,
                title: title,
                blockId: block.id
              })
            }
          }
        }
      })
      
      // Sort by block order
      newToc.sort((a, b) => {
        const aBlock = content[a.blockId]
        const bBlock = content[b.blockId]
        return (aBlock?.meta?.order || 0) - (bBlock?.meta?.order || 0)
      })
      
      setTocItems(newToc)
    } catch (error) {
      console.error('Error updating TOC:', error)
    }
  }
  
  // Update TOC when value changes
  useEffect(() => {
    updateTOCFromContent(value)
  }, [value])

  // Simplified and more reliable scroll function
  const scrollToSection = (tocItem: {id: string, title: string, blockId: string}) => {
    console.log('🔍 Scrolling to:', tocItem.title)
    setSelectedTocItem(tocItem.id)
    
    // Give the UI a moment to update selection state
    setTimeout(() => {
      let targetElement: Element | null = null
      
      // Strategy 1: Find by exact text match in headings
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
      console.log('📋 Found', headings.length, 'headings on page')
      
      for (const heading of headings) {
        const headingText = heading.textContent?.trim() || ''
        console.log('📝 Checking heading:', headingText)
        
        if (headingText === tocItem.title) {
          targetElement = heading
          console.log('✅ Exact match found!')
          break
        }
      }
      
      // Strategy 2: Find by partial text match if exact match failed
      if (!targetElement) {
        console.log('🔍 Trying partial text match...')
        for (const heading of headings) {
          const headingText = heading.textContent?.trim() || ''
          if (headingText.includes(tocItem.title) || tocItem.title.includes(headingText)) {
            targetElement = heading
            console.log('✅ Partial match found:', headingText)
            break
          }
        }
      }
      
      // Strategy 3: Find any element containing the title text
      if (!targetElement) {
        console.log('🔍 Trying any element with text...')
        const allElements = document.querySelectorAll('*')
        for (const element of allElements) {
          const text = element.textContent?.trim() || ''
          if (text.startsWith(tocItem.title) && text.length < tocItem.title.length + 50) {
            targetElement = element
            console.log('✅ Text match found in:', element.tagName)
            break
          }
        }
      }
      
      if (targetElement) {
        console.log('🎯 Scrolling to element:', targetElement.tagName, targetElement.textContent?.substring(0, 50))
        
        // Find the editor's scrollable container specifically
        const editorContainer = editorContainerRef.current
        console.log('📦 Editor container:', editorContainer)
        
        if (editorContainer) {
          const containerRect = editorContainer.getBoundingClientRect()
          const targetRect = targetElement.getBoundingClientRect()
          
          // Calculate the target's position relative to the editor container
          const currentScrollTop = editorContainer.scrollTop
          const targetOffsetTop = targetRect.top - containerRect.top + currentScrollTop
          const targetScrollPosition = targetOffsetTop - 80 // 80px padding from top
          
          console.log('📐 Editor container scroll calculation:', {
            containerRect: {
              top: containerRect.top,
              height: containerRect.height,
              scrollTop: currentScrollTop
            },
            targetRect: {
              top: targetRect.top,
              height: targetRect.height
            },
            calculations: {
              targetOffsetTop,
              targetScrollPosition,
              finalScroll: Math.max(0, targetScrollPosition)
            }
          })
          
          // Scroll the editor container
          editorContainer.scrollTo({
            top: Math.max(0, targetScrollPosition),
            behavior: 'smooth'
          })
          
          console.log('✅ Editor container scroll initiated')
        } else {
          console.log('⚠️ Editor container not found, trying alternative method')
          
          // Try to find any parent scroll container
          const scrollableParent = targetElement.closest('.overflow-auto') || 
                                  targetElement.closest('[style*="overflow"]') ||
                                  document.querySelector('.overflow-auto')
          
          if (scrollableParent && scrollableParent !== document.documentElement) {
            console.log('📦 Found scrollable parent:', scrollableParent.className)
            
            const containerRect = scrollableParent.getBoundingClientRect()
            const targetRect = targetElement.getBoundingClientRect()
            const currentScrollTop = scrollableParent.scrollTop
            const targetOffsetTop = targetRect.top - containerRect.top + currentScrollTop
            const targetScrollPosition = targetOffsetTop - 80
            
            scrollableParent.scrollTo({
              top: Math.max(0, targetScrollPosition),
              behavior: 'smooth'
            })
            
            console.log('✅ Parent container scroll initiated')
          } else {
            // Last resort: direct scrollIntoView on the target
            console.log('📜 Using direct scrollIntoView as last resort')
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            })
          }
        }
        
        // Visual feedback
        const originalBackground = (targetElement as HTMLElement).style.backgroundColor
        const originalTransition = (targetElement as HTMLElement).style.transition
        
        ;(targetElement as HTMLElement).style.backgroundColor = 'rgba(59, 130, 246, 0.15)'
        ;(targetElement as HTMLElement).style.transition = 'background-color 0.3s ease'
        
        setTimeout(() => {
          ;(targetElement as HTMLElement).style.backgroundColor = originalBackground
          ;(targetElement as HTMLElement).style.transition = originalTransition
        }, 1500)
        
        console.log('✅ Scroll completed successfully')
        
      } else {
        console.error('❌ Could not find target element for:', tocItem.title)
        console.log('🔍 Available headings:', Array.from(headings).map(h => h.textContent?.trim().substring(0, 50)))
        
        // Last resort: scroll to top of editor
        const editorElement = document.querySelector('.yoopta-editor') || editorContainerRef.current
        if (editorElement) {
          editorElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      }
    }, 100)
  }

  return (
    <div className="h-full flex">
      {/* Simple TOC Sidebar */}
      <div className="w-80 bg-slate-50 border-r border-slate-200 flex flex-col">
        <div className="p-4 border-b border-slate-200 bg-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-slate-900">Table of Contents</h3>
              <p className="text-sm text-slate-600 mt-1">Report sections</p>
            </div>
            <button
              type="button"
              onClick={() => {
                console.log('🔍 DEBUG: Available headings in DOM:')
                const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
                headings.forEach((h, i) => {
                  console.log(`${i + 1}. ${h.tagName}: "${h.textContent?.trim()}"`)
                })
                console.log('📋 Current TOC items:')
                tocItems.forEach((item, i) => {
                  console.log(`${i + 1}. "${item.title}" (ID: ${item.blockId})`)
                })
                console.log('📦 Editor container info:')
                console.log('- Editor container ref:', editorContainerRef.current)
                console.log('- Container scrollTop:', editorContainerRef.current?.scrollTop)
                console.log('- Container scrollHeight:', editorContainerRef.current?.scrollHeight)
                console.log('- Container clientHeight:', editorContainerRef.current?.clientHeight)
                
                console.log('🔍 Available scroll containers:')
                const scrollContainers = document.querySelectorAll('.overflow-auto')
                scrollContainers.forEach((container, i) => {
                  console.log(`${i + 1}. ${container.tagName}.${container.className} - scrollTop: ${container.scrollTop}`)
                })
              }}
              className="px-2 py-1 text-xs bg-slate-100 hover:bg-slate-200 rounded text-slate-600"
            >
              Debug
            </button>
          </div>
        </div>
        
        <div className="flex-1 overflow-auto p-4 space-y-2">
          {tocItems.map((item) => {
            const isSelected = selectedTocItem === item.id
            let colorClass = 'bg-white border-slate-200'
            let textClass = 'text-slate-700'
            let subtextClass = 'text-slate-500'
            let icon = '📄'
            
            // Customize appearance based on section
            if (item.title.includes('Executive Summary')) {
              icon = '📋'
            } else if (item.title.includes('Statistics')) {
              icon = '📊'
            } else if (item.title.includes('AI Analysis')) {
              icon = '🤖'
            } else if (item.title.includes('Comparison')) {
              colorClass = 'bg-orange-50 border-orange-200'
              textClass = 'text-orange-800'
              subtextClass = 'text-orange-600'
              icon = '🔍'
            } else if (item.title.includes('Findings')) {
              colorClass = 'bg-red-50 border-red-200'
              textClass = 'text-red-800'
              subtextClass = 'text-red-600'
              icon = '🚨'
            } else if (item.title.includes('Recommendations')) {
              colorClass = 'bg-blue-50 border-blue-200'
              textClass = 'text-blue-800'
              subtextClass = 'text-blue-600'
              icon = '📋'
            } else if (item.title.includes('Technical')) {
              colorClass = 'bg-slate-50 border-slate-200'
              textClass = 'text-slate-700'
              subtextClass = 'text-slate-500'
              icon = '🔧'
            }
            
            if (isSelected) {
              colorClass += ' ring-2 ring-blue-500 ring-offset-1'
            }
            
            return (
              <button
                key={item.id}
                type="button"
                onClick={() => scrollToSection(item)}
                className={`w-full text-left p-3 rounded-lg border hover:shadow-sm cursor-pointer transition-all duration-200 ${colorClass}`}
              >
                <div className={`text-sm font-medium ${textClass}`}>
                  {icon} {item.title}
                </div>
                <div className={`text-xs mt-1 ${subtextClass}`}>
                  {item.title === reportData.title ? 'Report Title' :
                   item.title.includes('Executive') ? 'Key Overview' :
                   item.title.includes('Statistics') ? 'Analysis Metrics' :
                   item.title.includes('Methodology') ? 'AI Methods' :
                   item.title.includes('Comparison') ? `${comparisonCards.length} AI Analyses` :
                   item.title.includes('Findings') ? `${selectedFindings.length} Issues Found` :
                   item.title.includes('Recommendations') ? 'Action Items' :
                   item.title.includes('Technical') ? 'AI Specifications' :
                   'Section'}
                </div>
              </button>
            )
          })}
          
          {tocItems.length === 0 && (
            <div className="text-center py-8 text-slate-500">
              <div className="text-sm">Loading table of contents...</div>
            </div>
          )}
        </div>
      </div>

      {/* Main Editor */}
      <div className="flex-1 flex flex-col bg-white">
        <div className="p-6 border-b border-slate-200">
          <h3 className="font-semibold text-slate-900">Report Editor</h3>
          <p className="text-sm text-slate-600 mt-1">
            Edit your report directly. Changes save automatically.
          </p>
        </div>
        
        <div className="flex-1 overflow-auto p-8" ref={editorContainerRef}>
          <div className="max-w-4xl mx-auto">
            <YooptaEditor
              editor={editor}
              plugins={plugins}
              tools={TOOLS}
              marks={MARKS}
              value={value}
              onChange={onChange}
              autoFocus
              style={{
                minHeight: '600px',
                width: '100%'
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}