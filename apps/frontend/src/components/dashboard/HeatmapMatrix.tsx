import { useMemo } from 'react'
import { <PERSON> } from '@tanstack/react-router'
import type { Tender } from '../../types'

interface HeatmapMatrixProps {
  project: Tender
  onCellClick: (pair: [string, string]) => void
  selectedPair: [string, string] | null
}

interface CompanyData {
  id: string
  name: string
  price: number
}

interface SimilarityData {
  company1: string
  company2: string
  overallRisk: number
  semanticSimilarity: number
  layoutSimilarity: number
  pricingSimilarity: number
}

export function HeatmapMatrix({ project, onCellClick, selectedPair }: HeatmapMatrixProps) {
  // Extract company data
  const companies: CompanyData[] = useMemo(() => 
    project.bids.map(bid => ({
      id: bid.id,
      name: bid.companyName,
      price: bid.totalPrice
    })), [project.bids]
  )

  // Mock similarity data - in real implementation this would come from API
  const similarityData: SimilarityData[] = useMemo(() => {
    const data: SimilarityData[] = []
    
    for (let i = 0; i < companies.length; i++) {
      for (let j = i + 1; j < companies.length; j++) {
        const company1 = companies[i]
        const company2 = companies[j]
        
        // Calculate mock similarities based on price difference and names
        const priceDiff = Math.abs(company1.price - company2.price) / Math.max(company1.price, company2.price)
        const overallRisk = Math.max(0.1, 0.9 - priceDiff * 2) + (Math.random() * 0.2 - 0.1)
        
        data.push({
          company1: company1.id,
          company2: company2.id,
          overallRisk: Math.max(0, Math.min(1, overallRisk)),
          semanticSimilarity: Math.max(0, Math.min(1, overallRisk + (Math.random() * 0.3 - 0.15))),
          layoutSimilarity: Math.max(0, Math.min(1, overallRisk + (Math.random() * 0.3 - 0.15))),
          pricingSimilarity: Math.max(0, Math.min(1, 0.9 - priceDiff + (Math.random() * 0.2 - 0.1)))
        })
      }
    }
    
    return data
  }, [companies])

  const getSimilarity = (company1Id: string, company2Id: string): SimilarityData | null => {
    return similarityData.find(data => 
      (data.company1 === company1Id && data.company2 === company2Id) ||
      (data.company1 === company2Id && data.company2 === company1Id)
    ) || null
  }

  const getRiskColor = (risk: number): string => {
    if (risk >= 0.8) return 'bg-red-500'
    if (risk >= 0.6) return 'bg-orange-500'
    if (risk >= 0.4) return 'bg-yellow-500'
    if (risk >= 0.2) return 'bg-blue-500'
    return 'bg-gray-400'
  }

  const getRiskLevel = (risk: number): string => {
    if (risk >= 0.8) return 'Critical'
    if (risk >= 0.6) return 'High'
    if (risk >= 0.4) return 'Medium'
    if (risk >= 0.2) return 'Low'
    return 'Minimal'
  }

  const isSelected = (company1Id: string, company2Id: string): boolean => {
    if (!selectedPair) return false
    return (selectedPair[0] === company1Id && selectedPair[1] === company2Id) ||
           (selectedPair[0] === company2Id && selectedPair[1] === company1Id)
  }

  return (
    <div className="space-y-6">
      {/* Risk Level Legend */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-6">
          <span className="text-sm font-medium text-slate-700">Risk Level:</span>
          <div className="flex items-center space-x-4">
            {[
              { level: 'Critical', color: 'bg-red-500', range: '80-100%' },
              { level: 'High', color: 'bg-orange-500', range: '60-79%' },
              { level: 'Medium', color: 'bg-yellow-500', range: '40-59%' },
              { level: 'Low', color: 'bg-blue-500', range: '20-39%' },
              { level: 'Minimal', color: 'bg-gray-400', range: '0-19%' }
            ].map(item => (
              <div key={item.level} className="flex items-center space-x-2">
                <div className={`w-4 h-4 rounded ${item.color}`} />
                <span className="text-xs text-slate-600">{item.level}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Heatmap Matrix */}
      <div className="relative border border-slate-200 rounded-lg overflow-hidden">
        <div className="overflow-auto max-h-[70vh]" style={{ maxWidth: '100%' }}>
          <table className="w-full border-collapse">
            {/* Header Row - Sticky */}
            <thead className="sticky top-0 z-20">
              <tr>
                <th className="sticky left-0 z-30 bg-white border-b-2 border-r-2 border-slate-300 p-2 w-48" />
                {companies.map((company, index) => (
                  <th key={company.id} className="bg-white border-b-2 border-slate-300 px-2 py-3 text-center relative group" style={{ minWidth: '120px' }}>
                    <div className="text-xs font-medium text-slate-700 truncate" title={company.name}>
                      {company.name}
                    </div>
                    <div className="text-xs text-slate-500 mt-1">
                      ${company.price?.toLocaleString() || 'N/A'}
                    </div>
                    
                    {/* Hover tooltip for full details */}
                    <div className="absolute z-50 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 bottom-full mb-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap pointer-events-none">
                      <div className="font-medium">{company.name}</div>
                      <div className="text-xs opacity-75">Bid: ${company.price?.toLocaleString() || 'N/A'}</div>
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            
            <tbody>
              {companies.map((rowCompany, rowIndex) => (
                <tr key={rowCompany.id}>
                  {/* Company Name Column - Sticky */}
                  <td className="sticky left-0 z-10 bg-slate-50 border-r-2 border-slate-300 p-2 w-48">
                    <div className="text-xs font-medium text-slate-900 truncate" title={rowCompany.name}>
                      {rowCompany.name}
                    </div>
                    <div className="text-xs text-slate-600 mt-1">
                      ${rowCompany.price?.toLocaleString() || 'N/A'}
                    </div>
                  </td>
                  
                  {/* Similarity Cells */}
                  {companies.map((colCompany, colIndex) => {
                    if (rowIndex === colIndex) {
                      // Diagonal cell (same company)
                      return (
                        <td key={colCompany.id} className="p-1" style={{ minWidth: '120px', height: '60px' }}>
                          <div className="w-full h-full bg-gray-100 rounded border border-gray-300 flex items-center justify-center">
                            <span className="text-xs text-gray-500">—</span>
                          </div>
                        </td>
                      )
                    }
                    
                    if (rowIndex > colIndex) {
                      // Lower triangle - empty
                      return (
                        <td key={colCompany.id} className="p-1" style={{ minWidth: '120px', height: '60px' }}>
                          <div className="w-full h-full" />
                        </td>
                      )
                    }
                    
                    // Upper triangle - similarity data
                    const similarity = getSimilarity(rowCompany.id, colCompany.id)
                    if (!similarity) return null
                    
                    const isSelectedCell = isSelected(rowCompany.id, colCompany.id)
                    
                    return (
                      <td key={colCompany.id} className="p-1 relative" style={{ minWidth: '120px', height: '60px' }}>
                        <Link
                          to="/comparison/$doc1/$doc2"
                          params={{
                            doc1: rowCompany.id,
                            doc2: colCompany.id
                          }}
                          className={`
                            w-full h-full rounded border cursor-pointer transition-all duration-200 
                            flex flex-col items-center justify-center text-center group hover:scale-110 hover:shadow-lg hover:z-40
                            ${getRiskColor(similarity.overallRisk)} text-white relative
                            ${isSelectedCell ? 'ring-2 ring-blue-600' : ''}
                          `}
                          onClick={() => onCellClick([rowCompany.id, colCompany.id])}
                        >
                          <div className="text-xs font-bold leading-tight">
                            {Math.round(similarity.overallRisk * 100)}%
                          </div>
                          <div className="text-xs opacity-90 leading-tight">
                            {getRiskLevel(similarity.overallRisk).slice(0, 3)}
                          </div>
                          
                          {/* Hover tooltip */}
                          <div className="absolute z-50 hidden group-hover:block bg-black text-white text-xs rounded p-2 bottom-full left-1/2 transform -translate-x-1/2 mb-1 whitespace-nowrap pointer-events-none">
                            <div className="text-center">
                              <div className="font-semibold">{rowCompany.name} vs {colCompany.name}</div>
                              <div>Overall Risk: {Math.round(similarity.overallRisk * 100)}%</div>
                              <div>Semantic: {Math.round(similarity.semanticSimilarity * 100)}%</div>
                              <div>Layout: {Math.round(similarity.layoutSimilarity * 100)}%</div>
                              <div>Pricing: {Math.round(similarity.pricingSimilarity * 100)}%</div>
                            </div>
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                          </div>
                        </Link>
                      </td>
                    )
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800 text-sm font-medium">Critical Risk Pairs</div>
          <div className="text-red-900 text-2xl font-bold mt-1">
            {similarityData.filter(s => s.overallRisk >= 0.8).length}
          </div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="text-orange-800 text-sm font-medium">High Risk Pairs</div>
          <div className="text-orange-900 text-2xl font-bold mt-1">
            {similarityData.filter(s => s.overallRisk >= 0.6 && s.overallRisk < 0.8).length}
          </div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="text-yellow-800 text-sm font-medium">Medium Risk Pairs</div>
          <div className="text-yellow-900 text-2xl font-bold mt-1">
            {similarityData.filter(s => s.overallRisk >= 0.4 && s.overallRisk < 0.6).length}
          </div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="text-blue-800 text-sm font-medium">Total Comparisons</div>
          <div className="text-blue-900 text-2xl font-bold mt-1">
            {similarityData.length}
          </div>
        </div>
      </div>
    </div>
  )
}