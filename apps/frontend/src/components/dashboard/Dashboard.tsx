import { useState } from 'react'
import { HeatmapMatrix } from './HeatmapMatrix'
import { StatisticsPanel } from './StatisticsPanel'
import { CompanyProfileSidebar } from './CompanyProfileSidebar'
import type { Tender } from '../../types'

interface DashboardProps {
  activeProject: Tender | null
  isLoading?: boolean
}

export function Dashboard({ activeProject, isLoading }: DashboardProps) {
  const [selectedCompanyPair, setSelectedCompanyPair] = useState<[string, string] | null>(null)
  const [showCompanyProfile, setShowCompanyProfile] = useState(false)

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-white">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" />
          <p className="text-slate-600">Loading project dashboard...</p>
        </div>
      </div>
    )
  }

  if (!activeProject) {
    return (
      <div className="h-full flex items-center justify-center bg-white">
        <div className="text-center space-y-6 max-w-lg mx-auto p-8">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl mx-auto flex items-center justify-center">
            <span className="text-white font-bold text-2xl">📊</span>
          </div>
          
          <div>
            <h1 className="text-3xl font-bold text-slate-900 mb-2">TraceFast Dashboard</h1>
            <p className="text-slate-600 text-lg">Select a project from the sidebar to view analysis dashboard</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-slate-50 flex">
      {/* Main Dashboard Content */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-slate-900">{activeProject.title}</h1>
                <p className="text-slate-600 mt-1">
                  {activeProject.department} • {activeProject.bids?.length || 0} bid documents
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm text-slate-600">Budget</div>
                  <div className="text-xl font-semibold text-slate-900">
                    ${activeProject.budget?.toLocaleString() || 'N/A'}
                  </div>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  activeProject.analysisStatus === 'completed' 
                    ? 'bg-green-100 text-green-800' 
                    : activeProject.analysisStatus === 'analyzing'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {activeProject.analysisStatus}
                </div>
              </div>
            </div>
          </div>

          {/* Statistics Panel */}
          <StatisticsPanel project={activeProject} />

          {/* Document List */}
          {activeProject.bids && activeProject.bids.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-slate-900">Uploaded Documents</h2>
                  <p className="text-slate-600 mt-1">
                    {activeProject.bids.length} documents processed and ready for analysis
                  </p>
                </div>
              </div>
              
              <div className="grid gap-4">
                {activeProject.bids.map((bid) => (
                  <div key={bid.id} className="border border-slate-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div>
                            <h3 className="font-medium text-slate-900">{bid.companyName}</h3>
                            <p className="text-sm text-slate-600">{bid.fileName}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="font-medium text-slate-900">${bid.totalPrice?.toLocaleString()}</div>
                          <div className="text-sm text-slate-600">{bid.uploadDate}</div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            type="button"
                            className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                            onClick={() => window.open(`/viewer/${bid.id}`, '_blank')}
                          >
                            View PDF + OCR
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Heatmap Matrix */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-bold text-slate-900">Company Similarity Matrix</h2>
                <p className="text-slate-600 mt-1">
                  Click on any cell to view detailed comparison analysis
                </p>
              </div>
              <button 
                type="button"
                onClick={() => setShowCompanyProfile(!showCompanyProfile)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {showCompanyProfile ? 'Hide' : 'Show'} Company Profiles
              </button>
            </div>
            
            <HeatmapMatrix 
              project={activeProject}
              onCellClick={setSelectedCompanyPair}
              selectedPair={selectedCompanyPair}
            />
          </div>
        </div>
      </div>

      {/* Company Profile Sidebar */}
      {showCompanyProfile && (
        <CompanyProfileSidebar 
          companies={(activeProject.bids || []).map(bid => ({
            name: bid.companyName,
            fileName: bid.fileName,
            totalPrice: bid.totalPrice,
            uploadDate: bid.uploadDate
          }))}
          selectedPair={selectedCompanyPair}
          onClose={() => setShowCompanyProfile(false)}
        />
      )}
    </div>
  )
}