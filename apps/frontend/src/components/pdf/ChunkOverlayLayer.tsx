// Chunk overlay layer for visualizing OCR bounding boxes on PDF pages

import type React from 'react';
import { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { CoordinateMapper } from '../../utils/coordinateMapper';
import type { DocumentChunk, ChunkOverlay, ChunkInteractionEvent } from '../../types/document';

interface ChunkOverlayLayerProps {
  chunks: DocumentChunk[];
  pageNumber: number;
  pageRef: React.RefObject<HTMLDivElement>;
  canvasRef: React.RefObject<HTMLCanvasElement>;
  scale: number;
  selectedChunkIds: Set<string>;
  highlightedChunkIds: Set<string>;
  comparisonChunks?: Map<string, { similarity: number; riskLevel: string }>;
  onChunkInteraction?: (event: ChunkInteractionEvent) => void;
  showLabels?: boolean;
  className?: string;
}

const CHUNK_TYPE_STYLES: Record<string, { borderColor: string; bgColor: string; hoverBgColor: string }> = {
  text: {
    borderColor: 'rgb(59 130 246)', // blue-500
    bgColor: 'rgba(59, 130, 246, 0.1)',
    hoverBgColor: 'rgba(59, 130, 246, 0.2)'
  },
  title: {
    borderColor: 'rgb(239 68 68)', // red-500
    bgColor: 'rgba(239, 68, 68, 0.1)',
    hoverBgColor: 'rgba(239, 68, 68, 0.2)'
  },
  table: {
    borderColor: 'rgb(34 197 94)', // green-500
    bgColor: 'rgba(34, 197, 94, 0.1)',
    hoverBgColor: 'rgba(34, 197, 94, 0.2)'
  },
  image: {
    borderColor: 'rgb(168 85 247)', // purple-500
    bgColor: 'rgba(168, 85, 247, 0.1)',
    hoverBgColor: 'rgba(168, 85, 247, 0.2)'
  },
  list: {
    borderColor: 'rgb(251 146 60)', // orange-500
    bgColor: 'rgba(251, 146, 60, 0.1)',
    hoverBgColor: 'rgba(251, 146, 60, 0.2)'
  }
};

export function ChunkOverlayLayer({
  chunks,
  pageNumber,
  pageRef,
  canvasRef,
  scale,
  selectedChunkIds,
  highlightedChunkIds,
  comparisonChunks,
  onChunkInteraction,
  showLabels = false,
  className = ''
}: ChunkOverlayLayerProps) {
  const overlayRef = useRef<HTMLDivElement>(null);

  // Filter chunks for this specific page
  const pageChunks = useMemo(() => {
    return chunks.filter(chunk => chunk.page_number === pageNumber);
  }, [chunks, pageNumber]);

  // Calculate overlay positions using canvas reference
  const overlays = useMemo(() => {
    if (!canvasRef.current || !pageRef.current || pageChunks.length === 0) return [];

    const canvas = canvasRef.current;
    const pageContainer = pageRef.current;

    // Get canvas dimensions and position
    const canvasRect = canvas.getBoundingClientRect();
    const containerRect = pageContainer.getBoundingClientRect();

    // Calculate the actual PDF page dimensions from the canvas
    const pdfPageWidth = canvas.width / scale;
    const pdfPageHeight = canvas.height / scale;

    return pageChunks.map(chunk => {
      // Convert PDF coordinates to display coordinates
      const displayCoords = CoordinateMapper.pdfToDisplay(
        chunk.bounding_box,
        pdfPageWidth,
        pdfPageHeight,
        scale
      );

      // Position relative to the canvas within the page container
      const relativeToCanvas = {
        x: displayCoords.x,
        y: displayCoords.y,
        width: displayCoords.width,
        height: displayCoords.height
      };

      return {
        chunk,
        displayCoords: relativeToCanvas,
        isHighlighted: highlightedChunkIds.has(chunk.id),
        isSelected: selectedChunkIds.has(chunk.id)
      };
    });
  }, [pageChunks, canvasRef, pageRef, scale, selectedChunkIds, highlightedChunkIds]);

  // Position overlay container to match the canvas exactly
  useEffect(() => {
    if (!canvasRef.current || !pageRef.current || !overlayRef.current) return;

    const canvas = canvasRef.current;
    const pageContainer = pageRef.current;
    const overlay = overlayRef.current;

    // Get the canvas position relative to the page container
    const canvasRect = canvas.getBoundingClientRect();
    const pageRect = pageContainer.getBoundingClientRect();

    // Position overlay to match the canvas exactly within the page container
    overlay.style.position = 'absolute';
    overlay.style.left = `${canvasRect.left - pageRect.left}px`;
    overlay.style.top = `${canvasRect.top - pageRect.top}px`;
    overlay.style.width = `${canvasRect.width}px`;
    overlay.style.height = `${canvasRect.height}px`;
    overlay.style.pointerEvents = 'auto'; // Allow interactions with overlays
    overlay.style.zIndex = '10';
  }, [canvasRef, pageRef, scale]);

  const handleChunkClick = useCallback((chunk: DocumentChunk, event: React.MouseEvent) => {
    if (!onChunkInteraction) return;

    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'click'
    });
  }, [onChunkInteraction, overlays]);

  const handleChunkKeyDown = useCallback((chunk: DocumentChunk, event: React.KeyboardEvent) => {
    if (!onChunkInteraction) return;

    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'click'
    });
  }, [onChunkInteraction, overlays]);

  const handleChunkHover = useCallback((chunk: DocumentChunk, event: React.MouseEvent) => {
    if (!onChunkInteraction) return;
    
    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'hover'
    });
  }, [onChunkInteraction, overlays]);

  if (overlays.length === 0) return null;

  return (
    <div
      ref={overlayRef}
      className={`chunk-overlay-layer ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
    >
      {overlays.map(({ chunk, displayCoords, isHighlighted, isSelected }) => {
        const style = CHUNK_TYPE_STYLES[chunk.chunk_type] || CHUNK_TYPE_STYLES.text;
        const comparisonData = comparisonChunks?.get(chunk.id);

        return (
          <div
            key={chunk.id}
            className={`absolute pointer-events-auto cursor-pointer transition-all duration-200 ${
              isSelected ? 'ring-2 ring-blue-500' : ''
            } ${isHighlighted ? 'ring-2 ring-yellow-400' : ''}`}
            style={{
              left: `${displayCoords.x}px`,
              top: `${displayCoords.y}px`,
              width: `${displayCoords.width}px`,
              height: `${displayCoords.height}px`,
              backgroundColor: isSelected 
                ? 'rgba(59, 130, 246, 0.3)' 
                : isHighlighted 
                ? 'rgba(251, 191, 36, 0.3)'
                : style.bgColor,
              border: `1px solid ${
                isSelected 
                  ? 'rgb(59, 130, 246)' 
                  : isHighlighted 
                  ? 'rgb(251, 191, 36)'
                  : style.borderColor
              }`
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleChunkClick(chunk, e);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                e.stopPropagation();
                handleChunkKeyDown(chunk, e);
              }
            }}
            onMouseEnter={(e) => {
              handleChunkHover(chunk, e);
            }}
            tabIndex={0}
            role="button"
            aria-label={`${chunk.chunk_type} chunk: ${chunk.content?.substring(0, 50) || ''}...`}
            title={`${chunk.chunk_type}: ${chunk.content?.substring(0, 100) || ''}...`}
          >
            {showLabels && (
              <div className="absolute -top-6 left-0 bg-black text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
                {chunk.chunk_type}
                {comparisonData && (
                  <span className="ml-1 text-yellow-300">
                    ({Math.round(comparisonData.similarity * 100)}%)
                  </span>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

// Utility hook for managing chunk overlays
export function useChunkOverlays(
  chunks: DocumentChunk[],
  initialSelection: Set<string> = new Set()
) {
  const [selectedChunks, setSelectedChunks] = useState<Set<string>>(initialSelection);
  const [highlightedChunks, setHighlightedChunks] = useState<Set<string>>(new Set());

  const selectChunk = useCallback((chunkId: string, multi = false) => {
    setSelectedChunks(prev => {
      const newSelection = new Set(multi ? prev : []);
      if (prev.has(chunkId)) {
        newSelection.delete(chunkId);
      } else {
        newSelection.add(chunkId);
      }
      return newSelection;
    });
  }, []);

  const highlightChunk = useCallback((chunkId: string) => {
    setHighlightedChunks(prev => {
      const newHighlights = new Set(prev);
      if (prev.has(chunkId)) {
        newHighlights.delete(chunkId);
      } else {
        newHighlights.add(chunkId);
      }
      return newHighlights;
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedChunks(new Set());
  }, []);

  const clearHighlights = useCallback(() => {
    setHighlightedChunks(new Set());
  }, []);

  const overlays = useMemo(() => {
    return chunks.map(chunk => ({
      chunk,
      displayCoords: { x: 0, y: 0, width: 0, height: 0 },
      isHighlighted: highlightedChunks.has(chunk.id),
      isSelected: selectedChunks.has(chunk.id)
    }));
  }, [chunks, selectedChunks, highlightedChunks]);

  return {
    overlays,
    selectedChunks,
    highlightedChunks,
    selectChunk,
    highlightChunk,
    clearSelection,
    clearHighlights
  };
}
