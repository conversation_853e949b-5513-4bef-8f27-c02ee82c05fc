// Chunk overlay layer for visualizing OCR bounding boxes on PDF pages

import type React from 'react';
import { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { CoordinateMapper } from '../../utils/coordinateMapper';
import type { DocumentChunk, ChunkOverlay, ChunkInteractionEvent } from '../../types/document';

interface ChunkOverlayLayerProps {
  chunks: DocumentChunk[];
  pageNumber: number;
  pageRef: React.RefObject<HTMLDivElement>;
  scale: number;
  selectedChunkIds: Set<string>;
  highlightedChunkIds: Set<string>;
  comparisonChunks?: Map<string, { similarity: number; riskLevel: string }>;
  onChunkInteraction?: (event: ChunkInteractionEvent) => void;
  showLabels?: boolean;
  className?: string;
}

const CHUNK_TYPE_STYLES: Record<string, { borderColor: string; bgColor: string; hoverBgColor: string }> = {
  text: {
    borderColor: 'rgb(59 130 246)', // blue-500
    bgColor: 'rgba(59, 130, 246, 0.1)',
    hoverBgColor: 'rgba(59, 130, 246, 0.2)'
  },
  title: {
    borderColor: 'rgb(239 68 68)', // red-500
    bgColor: 'rgba(239, 68, 68, 0.1)',
    hoverBgColor: 'rgba(239, 68, 68, 0.2)'
  },
  table: {
    borderColor: 'rgb(34 197 94)', // green-500
    bgColor: 'rgba(34, 197, 94, 0.1)',
    hoverBgColor: 'rgba(34, 197, 94, 0.2)'
  },
  image: {
    borderColor: 'rgb(168 85 247)', // purple-500
    bgColor: 'rgba(168, 85, 247, 0.1)',
    hoverBgColor: 'rgba(168, 85, 247, 0.2)'
  },
  list: {
    borderColor: 'rgb(251 146 60)', // orange-500
    bgColor: 'rgba(251, 146, 60, 0.1)',
    hoverBgColor: 'rgba(251, 146, 60, 0.2)'
  }
};

export function ChunkOverlayLayer({
  chunks,
  pageNumber,
  pageRef,
  scale,
  selectedChunkIds,
  highlightedChunkIds,
  comparisonChunks,
  onChunkInteraction,
  showLabels = false,
  className = ''
}: ChunkOverlayLayerProps) {
  const overlayRef = useRef<HTMLDivElement>(null);

  // Filter chunks for this specific page
  const pageChunks = useMemo(() => {
    return chunks.filter(chunk => chunk.page_number === pageNumber);
  }, [chunks, pageNumber]);

  // Calculate overlay positions
  const overlays = useMemo(() => {
    if (!pageRef.current || pageChunks.length === 0) return [];

    // Get the actual page element to determine PDF page dimensions
    const pageElement = pageRef.current.querySelector('canvas');
    if (!pageElement) return [];

    // Calculate PDF page dimensions from the rendered page
    // The canvas represents the actual PDF page at the current scale
    const pdfPageWidth = pageElement.width / scale;
    const pdfPageHeight = pageElement.height / scale;

    return pageChunks.map(chunk => {
      const displayCoords = CoordinateMapper.pdfToDisplay(
        chunk.bounding_box,
        pdfPageWidth,
        pdfPageHeight,
        scale
      );

      return {
        chunk,
        displayCoords,
        isHighlighted: highlightedChunkIds.has(chunk.id),
        isSelected: selectedChunkIds.has(chunk.id)
      };
    });
  }, [pageChunks, pageRef, scale, selectedChunkIds, highlightedChunkIds, pageNumber]);

  // Position overlay container relative to page
  useEffect(() => {
    if (!pageRef.current || !overlayRef.current) return;

    const pageElement = pageRef.current;
    const pageRect = pageElement.getBoundingClientRect();
    const viewerContainer = pageElement.closest('.pdf-viewer');
    
    if (!viewerContainer) return;

    const containerRect = viewerContainer.getBoundingClientRect();
    
    // Position overlay to match the page
    overlayRef.current.style.position = 'absolute';
    overlayRef.current.style.left = `${pageRect.left - containerRect.left}px`;
    overlayRef.current.style.top = `${pageRect.top - containerRect.top}px`;
    overlayRef.current.style.width = `${pageRect.width}px`;
    overlayRef.current.style.height = `${pageRect.height}px`;
    overlayRef.current.style.pointerEvents = 'none';
  }, [pageRef, scale, pageNumber]);

  const handleChunkClick = useCallback((chunk: DocumentChunk, event: React.MouseEvent) => {
    if (!onChunkInteraction) return;
    
    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'click'
    });
  }, [onChunkInteraction, overlays]);

  const handleChunkHover = useCallback((chunk: DocumentChunk, event: React.MouseEvent) => {
    if (!onChunkInteraction) return;
    
    const overlay = overlays.find(o => o.chunk.id === chunk.id);
    if (!overlay) return;

    onChunkInteraction({
      chunk,
      overlay,
      event,
      action: 'hover'
    });
  }, [onChunkInteraction, overlays]);

  if (overlays.length === 0) return null;

  return (
    <div
      ref={overlayRef}
      className={`chunk-overlay-layer ${className}`}
      style={{ zIndex: 10 }}
    >
      {overlays.map(({ chunk, displayCoords, isHighlighted, isSelected }) => {
        const style = CHUNK_TYPE_STYLES[chunk.chunk_type] || CHUNK_TYPE_STYLES.text;
        const comparisonData = comparisonChunks?.get(chunk.id);

        return (
          <div
            key={chunk.id}
            className={`absolute pointer-events-auto cursor-pointer transition-all duration-200 ${
              isSelected ? 'ring-2 ring-blue-500' : ''
            } ${isHighlighted ? 'ring-2 ring-yellow-400' : ''}`}
            style={{
              left: `${displayCoords.x}px`,
              top: `${displayCoords.y}px`,
              width: `${displayCoords.width}px`,
              height: `${displayCoords.height}px`,
              backgroundColor: isSelected 
                ? 'rgba(59, 130, 246, 0.3)' 
                : isHighlighted 
                ? 'rgba(251, 191, 36, 0.3)'
                : style.bgColor,
              border: `1px solid ${
                isSelected 
                  ? 'rgb(59, 130, 246)' 
                  : isHighlighted 
                  ? 'rgb(251, 191, 36)'
                  : style.borderColor
              }`
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleChunkClick(chunk, e);
            }}
            onMouseEnter={(e) => {
              handleChunkHover(chunk, e);
            }}
            title={`${chunk.chunk_type}: ${chunk.content?.substring(0, 100) || ''}...`}
          >
            {showLabels && (
              <div className="absolute -top-6 left-0 bg-black text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
                {chunk.chunk_type}
                {comparisonData && (
                  <span className="ml-1 text-yellow-300">
                    ({Math.round(comparisonData.similarity * 100)}%)
                  </span>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

// Utility hook for managing chunk overlays
export function useChunkOverlays(
  chunks: DocumentChunk[],
  initialSelection: Set<string> = new Set()
) {
  const [selectedChunks, setSelectedChunks] = useState<Set<string>>(initialSelection);
  const [highlightedChunks, setHighlightedChunks] = useState<Set<string>>(new Set());

  const selectChunk = useCallback((chunkId: string, multi = false) => {
    setSelectedChunks(prev => {
      const newSelection = new Set(multi ? prev : []);
      if (prev.has(chunkId)) {
        newSelection.delete(chunkId);
      } else {
        newSelection.add(chunkId);
      }
      return newSelection;
    });
  }, []);

  const highlightChunk = useCallback((chunkId: string) => {
    setHighlightedChunks(prev => {
      const newHighlights = new Set(prev);
      if (prev.has(chunkId)) {
        newHighlights.delete(chunkId);
      } else {
        newHighlights.add(chunkId);
      }
      return newHighlights;
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedChunks(new Set());
  }, []);

  const clearHighlights = useCallback(() => {
    setHighlightedChunks(new Set());
  }, []);

  const overlays = useMemo(() => {
    return chunks.map(chunk => ({
      chunk,
      displayCoords: { x: 0, y: 0, width: 0, height: 0 },
      isHighlighted: highlightedChunks.has(chunk.id),
      isSelected: selectedChunks.has(chunk.id)
    }));
  }, [chunks, selectedChunks, highlightedChunks]);

  return {
    overlays,
    selectedChunks,
    highlightedChunks,
    selectChunk,
    highlightChunk,
    clearSelection,
    clearHighlights
  };
}
