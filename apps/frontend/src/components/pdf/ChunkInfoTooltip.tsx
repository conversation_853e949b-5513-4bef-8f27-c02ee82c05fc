// Chunk information tooltip component

import { useState, useEffect, useRef } from 'react';
import { FileText, Hash, MapPin, Zap, Copy, ExternalLink } from 'lucide-react';
import { Button } from '../ui/button';
import type { DocumentChunk, TooltipData } from '../../types/document';

interface ChunkInfoTooltipProps {
  tooltip: TooltipData | null;
  onClose: () => void;
  onCompareChunk?: (chunkId: string) => void;
  onHighlightSimilar?: (chunkId: string) => void;
  className?: string;
}

export function ChunkInfoTooltip({
  tooltip,
  onClose,
  onCompareChunk,
  onHighlightSimilar,
  className = ''
}: ChunkInfoTooltipProps) {
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Show/hide with animation
  useEffect(() => {
    if (tooltip?.isVisible) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => setIsVisible(false), 200);
      return () => clearTimeout(timer);
    }
  }, [tooltip?.isVisible]);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isVisible, onClose]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isVisible, onClose]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const truncateText = (text: string, maxLength = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const formatConfidence = (confidence?: number) => {
    if (confidence === undefined) return 'N/A';
    return `${Math.round(confidence * 100)}%`;
  };

  const getChunkTypeIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <FileText className="w-4 h-4" />;
      case 'title':
        return <Hash className="w-4 h-4" />;
      case 'table':
        return <div className="w-4 h-4 border-2 border-current" />;
      case 'image':
        return <div className="w-4 h-4 bg-current rounded" />;
      case 'list':
        return <div className="w-4 h-4 flex flex-col space-y-0.5">
          <div className="w-full h-0.5 bg-current" />
          <div className="w-full h-0.5 bg-current" />
          <div className="w-full h-0.5 bg-current" />
        </div>;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getRiskLevelColor = (riskLevel?: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-red-500 bg-red-50 border-red-200';
      case 'medium':
        return 'text-orange-500 bg-orange-50 border-orange-200';
      case 'low':
        return 'text-green-500 bg-green-50 border-green-200';
      default:
        return 'text-gray-500 bg-gray-50 border-gray-200';
    }
  };

  if (!tooltip || !isVisible) {
    return null;
  }

  const { chunk, position, comparison } = tooltip;

  return (
    <div
      ref={tooltipRef}
      className={`fixed z-50 w-80 max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg transition-all duration-200 ${
        tooltip.isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
      } ${className}`}
      style={{
        left: position.x,
        top: position.y,
        maxHeight: '400px'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="text-blue-600">
            {getChunkTypeIcon(chunk.chunk_type)}
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900 capitalize">
              {chunk.chunk_type} Chunk
            </h3>
            <p className="text-xs text-gray-500">
              Page {chunk.page_number} • ID: {chunk.id.substring(0, 8)}...
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
        >
          ×
        </Button>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3 max-h-64 overflow-y-auto">
        {/* Chunk Content */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-xs font-medium text-gray-700">Content</label>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(chunk.content)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
            >
              <Copy className="w-3 h-3" />
            </Button>
          </div>
          <div className="text-sm text-gray-800 bg-gray-50 p-2 rounded border font-mono leading-relaxed">
            {truncateText(chunk.content)}
          </div>
        </div>

        {/* Metadata */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-xs">
            <MapPin className="w-3 h-3 text-gray-400" />
            <span className="text-gray-600">
              Position: ({Math.round(chunk.bounding_box.x1)}, {Math.round(chunk.bounding_box.y1)}) - ({Math.round(chunk.bounding_box.x2)}, {Math.round(chunk.bounding_box.y2)})
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-xs">
            <div className="w-3 h-3 border border-gray-400" />
            <span className="text-gray-600">
              Size: {Math.round(chunk.bounding_box.width)} × {Math.round(chunk.bounding_box.height)}
            </span>
          </div>

          {chunk.confidence !== undefined && (
            <div className="flex items-center space-x-2 text-xs">
              <Zap className="w-3 h-3 text-gray-400" />
              <span className="text-gray-600">
                Confidence: {formatConfidence(chunk.confidence)}
              </span>
            </div>
          )}
        </div>

        {/* Comparison Info */}
        {comparison && (
          <div className="space-y-2 pt-2 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <label className="text-xs font-medium text-gray-700">Comparison</label>
              <span className={`px-2 py-1 text-xs font-medium rounded border ${getRiskLevelColor(comparison.riskLevel)}`}>
                {comparison.riskLevel.toUpperCase()}
              </span>
            </div>
            
            <div className="text-sm text-gray-800">
              <div className="flex items-center justify-between mb-1">
                <span>Similarity:</span>
                <span className="font-semibold">
                  {Math.round(comparison.similarity * 100)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    comparison.similarity >= 0.9
                      ? 'bg-red-500'
                      : comparison.similarity >= 0.7
                      ? 'bg-orange-500'
                      : 'bg-green-500'
                  }`}
                  style={{ width: `${comparison.similarity * 100}%` }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Compared with: {comparison.comparedWith}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-2 px-4 py-3 border-t border-gray-200 bg-gray-50">
        {onCompareChunk && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCompareChunk(chunk.id)}
            className="text-xs"
          >
            <ExternalLink className="w-3 h-3 mr-1" />
            Compare
          </Button>
        )}
        
        {onHighlightSimilar && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onHighlightSimilar(chunk.id)}
            className="text-xs"
          >
            <Zap className="w-3 h-3 mr-1" />
            Find Similar
          </Button>
        )}
      </div>
    </div>
  );
}

// Utility hook for managing tooltip state
export function useChunkTooltip() {
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);

  const showTooltip = (
    chunk: DocumentChunk,
    position: { x: number; y: number },
    comparison?: {
      similarity: number;
      riskLevel: string;
      comparedWith: string;
    }
  ) => {
    setTooltip({
      chunk,
      position,
      isVisible: true,
      comparison
    });
  };

  const hideTooltip = () => {
    setTooltip(prev => prev ? { ...prev, isVisible: false } : null);
  };

  const updateTooltipPosition = (position: { x: number; y: number }) => {
    setTooltip(prev => prev ? { ...prev, position } : null);
  };

  return {
    tooltip,
    showTooltip,
    hideTooltip,
    updateTooltipPosition
  };
}