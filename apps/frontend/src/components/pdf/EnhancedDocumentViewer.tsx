// Enhanced Document Viewer with OCR chunk visualization

import { useState, useCallback, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader2, AlertTriangle, Eye, EyeOff, Filter, Search } from 'lucide-react';
import { PDFViewerCore } from './PDFViewerCore';
import { ChunkOverlayLayer, useChunkOverlays } from './ChunkOverlayLayer';
import { ChunkInfoTooltip, useChunkTooltip } from './ChunkInfoTooltip';
import { Button } from '../ui/button';
import { documentService } from '../../services/documentService';
import type { 
  DocumentChunk, 
  ChunkInteractionEvent, 
  ChunkFilterOptions,
  DocumentComparisonResult 
} from '../../types/document';

interface EnhancedDocumentViewerProps {
  documentId: string;
  documentUrl?: string;
  comparisonDocumentId?: string;
  comparisonResult?: DocumentComparisonResult;
  mode?: 'single' | 'comparison';
  showControls?: boolean;
  showChunks?: boolean;
  showOcrOverlay?: boolean;
  initialChunkFilters?: Partial<ChunkFilterOptions>;
  onChunkSelect?: (chunkIds: string[]) => void;
  onChunkCompare?: (chunkId: string) => void;
  className?: string;
}

export function EnhancedDocumentViewer({
  documentId,
  documentUrl,
  comparisonDocumentId,
  comparisonResult,
  mode = 'single',
  showControls = true,
  showChunks = true,
  showOcrOverlay = true,
  initialChunkFilters = {},
  onChunkSelect,
  onChunkCompare,
  className = ''
}: EnhancedDocumentViewerProps) {
  const [pdfState, setPdfState] = useState({
    currentPage: 1,
    totalPages: 0,
    scale: 1.0
  });

  const [chunkFilters, setChunkFilters] = useState<ChunkFilterOptions>({
    chunkTypes: new Set(['text', 'title', 'table', 'image', 'list']),
    similarityThreshold: 0.5,
    pageRange: null,
    searchText: '',
    showOnlyHighlighted: false,
    ...initialChunkFilters
  });

  const [chunksVisible, setChunksVisible] = useState(showChunks);

  // Fetch document chunks
  const {
    data: chunksData,
    isLoading: chunksLoading,
    error: chunksError,
    refetch: refetchChunks
  } = useQuery({
    queryKey: ['document-chunks', documentId],
    queryFn: () => documentService.getDocumentChunks(documentId),
    enabled: !!documentId && chunksVisible
  });

  // Fetch document metadata
  const { data: documentMetadata } = useQuery({
    queryKey: ['document-metadata', documentId],
    queryFn: () => documentService.getDocumentMetadata(documentId),
    enabled: !!documentId
  });

  // Filter chunks based on current filters
  const filteredChunks = useMemo(() => {
    if (!chunksData?.chunks) return [];

    let chunks = chunksData.chunks;

    // Filter by chunk types
    chunks = chunks.filter(chunk => chunkFilters.chunkTypes.has(chunk.chunk_type));

    // Filter by page range
    if (chunkFilters.pageRange) {
      const [minPage, maxPage] = chunkFilters.pageRange;
      chunks = chunks.filter(chunk => 
        chunk.page_number >= minPage && chunk.page_number <= maxPage
      );
    }

    // Filter by search text
    if (chunkFilters.searchText.trim()) {
      const searchText = chunkFilters.searchText.toLowerCase();
      chunks = chunks.filter(chunk =>
        chunk.content.toLowerCase().includes(searchText)
      );
    }

    return chunks;
  }, [chunksData?.chunks, chunkFilters]);

  // Create comparison chunks map from comparison result
  const comparisonChunks = useMemo(() => {
    if (!comparisonResult?.similar_chunks) return new Map();

    const map = new Map<string, { similarity: number; riskLevel: string }>();
    
    comparisonResult.similar_chunks.forEach(pair => {
      // Map source chunks
      if (pair.sourceChunk.id) {
        map.set(pair.sourceChunk.id, {
          similarity: pair.similarity,
          riskLevel: pair.riskLevel
        });
      }
    });

    return map;
  }, [comparisonResult]);

  // Chunk overlay management
  const {
    selectedChunks,
    highlightedChunks,
    selectChunk,
    highlightChunk,
    clearSelection,
    clearHighlights
  } = useChunkOverlays(filteredChunks);

  // Tooltip management
  const {
    tooltip,
    showTooltip,
    hideTooltip
  } = useChunkTooltip();

  // Handle chunk interactions
  const handleChunkInteraction = useCallback((event: ChunkInteractionEvent) => {
    const { chunk, action, event: mouseEvent } = event;

    switch (action) {
      case 'click':
        if (mouseEvent.ctrlKey || mouseEvent.metaKey) {
          selectChunk(chunk.id, true); // Multi-select
        } else {
          selectChunk(chunk.id, false); // Single select
        }
        
        // Notify parent component
        onChunkSelect?.(Array.from(selectedChunks).concat([chunk.id]));
        break;

      case 'hover':
        const rect = (mouseEvent.target as HTMLElement).getBoundingClientRect();
        const comparison = comparisonChunks.get(chunk.id);
        
        showTooltip(
          chunk,
          { x: rect.right + 10, y: rect.top },
          comparison ? {
            similarity: comparison.similarity,
            riskLevel: comparison.riskLevel,
            comparedWith: comparisonResult?.document2_name || 'Unknown'
          } : undefined
        );
        break;
    }
  }, [
    selectChunk, 
    selectedChunks, 
    onChunkSelect, 
    showTooltip, 
    comparisonChunks, 
    comparisonResult
  ]);

  // Handle chunk filtering
  const updateFilter = useCallback(<K extends keyof ChunkFilterOptions>(
    key: K,
    value: ChunkFilterOptions[K]
  ) => {
    setChunkFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const toggleChunkType = useCallback((chunkType: string) => {
    setChunkFilters(prev => {
      const newTypes = new Set(prev.chunkTypes);
      if (newTypes.has(chunkType)) {
        newTypes.delete(chunkType);
      } else {
        newTypes.add(chunkType);
      }
      return { ...prev, chunkTypes: newTypes };
    });
  }, []);

  // Handle search for similar chunks
  const handleHighlightSimilar = useCallback(async (chunkId: string) => {
    try {
      const chunk = filteredChunks.find(c => c.id === chunkId);
      if (!chunk) return;

      const similarChunks = await documentService.searchSimilarChunks(
        documentId,
        chunk.content,
        0.7,
        10
      );

      // Highlight similar chunks
      similarChunks.forEach(similar => {
        highlightChunk(similar.chunk_id);
      });
    } catch (error) {
      console.error('Failed to find similar chunks:', error);
    }
  }, [documentId, filteredChunks, highlightChunk]);

  // Render chunk filter panel
  const renderChunkFilters = () => (
    <div className="flex items-center space-x-2 p-2 border-b border-gray-200 bg-gray-50">
      {/* Visibility toggle */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setChunksVisible(!chunksVisible)}
        className={`${chunksVisible ? 'bg-blue-50 border-blue-200 text-blue-700' : ''}`}
      >
        {chunksVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
        <span className="ml-1 text-xs">
          Chunks ({filteredChunks.length})
        </span>
      </Button>

      {chunksVisible && (
        <>
          {/* Chunk type filters */}
          <div className="flex items-center space-x-1">
            {(['text', 'title', 'table', 'image', 'list'] as const).map(type => (
              <Button
                key={type}
                variant="outline"
                size="sm"
                onClick={() => toggleChunkType(type)}
                className={`text-xs px-2 py-1 h-7 ${
                  chunkFilters.chunkTypes.has(type)
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'text-gray-600'
                }`}
              >
                {type}
              </Button>
            ))}
          </div>

          {/* Search input */}
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search chunks..."
              value={chunkFilters.searchText}
              onChange={(e) => updateFilter('searchText', e.target.value)}
              className="pl-7 pr-3 py-1 text-xs border border-gray-300 rounded w-32 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          {/* Clear buttons */}
          {selectedChunks.size > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearSelection}
              className="text-xs"
            >
              Clear Selection ({selectedChunks.size})
            </Button>
          )}

          {highlightedChunks.size > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearHighlights}
              className="text-xs"
            >
              Clear Highlights ({highlightedChunks.size})
            </Button>
          )}
        </>
      )}
    </div>
  );

  // Handle loading and error states
  if (chunksLoading && chunksVisible) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin text-blue-600" />
          <p className="text-sm text-gray-600">Loading document chunks...</p>
        </div>
      </div>
    );
  }

  if (chunksError && chunksVisible) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Chunks</h3>
          <p className="text-sm text-gray-600 mb-4">
            {chunksError.message || 'An unexpected error occurred.'}
          </p>
          <Button variant="outline" onClick={() => refetchChunks()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`enhanced-document-viewer flex flex-col h-full ${className}`}>
      {renderChunkFilters()}

      {/* PDF Viewer with overlays */}
      <div className="flex-1 relative overflow-hidden">
        <PDFViewerCore
          documentUrl={documentUrl || documentService.getDocumentUrl(documentId)}
          showControls={showControls}
          onStateChange={setPdfState}
          className="h-full"
        >
          {({ currentPage, totalPages, scale, pageRefs }) => (
            <div className="absolute inset-0 pointer-events-none">
              {/* Chunk overlays for each page */}
              {chunksVisible &&
                pageRefs.map((pageRef, index) => {
                  const pageNumber = index + 1;
                  return (
                    <ChunkOverlayLayer
                      key={pageNumber}
                      chunks={filteredChunks}
                      pageNumber={pageNumber}
                      pageRef={pageRef}
                      scale={scale}
                      selectedChunkIds={selectedChunks}
                      highlightedChunkIds={highlightedChunks}
                      comparisonChunks={comparisonChunks}
                      onChunkInteraction={handleChunkInteraction}
                      showLabels={scale >= 1.2}
                      className="pointer-events-auto"
                    />
                  );
                })}
            </div>
          )}
        </PDFViewerCore>

        {/* Chunk info tooltip */}
        <ChunkInfoTooltip
          tooltip={tooltip}
          onClose={hideTooltip}
          onCompareChunk={onChunkCompare}
          onHighlightSimilar={handleHighlightSimilar}
        />
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-4 py-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-600">
        <div>
          {documentMetadata && (
            <span>{documentMetadata.filename} • {documentMetadata.company_name}</span>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {chunksVisible && (
            <span>{filteredChunks.length} chunks visible</span>
          )}
          {selectedChunks.size > 0 && (
            <span>{selectedChunks.size} selected</span>
          )}
          {comparisonResult && (
            <span>
              {comparisonResult.similar_chunks_found} similar chunks found
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
