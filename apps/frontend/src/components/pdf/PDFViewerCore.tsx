// Core PDF viewer component using react-pdf

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Document, Page } from 'react-pdf';
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '../ui/button';
import type { PDFViewerState, ViewerControls } from '../../types/document';

// Import PDF.js configuration
import '../../lib/pdf-config';

interface PDFViewerCoreProps {
  documentUrl?: string;
  file?: File | ArrayBuffer | string;
  className?: string;
  initialPage?: number;
  initialScale?: number;
  showControls?: boolean;
  onStateChange?: (state: PDFViewerState) => void;
  onPageChange?: (page: number) => void;
  onScaleChange?: (scale: number) => void;
  onDocumentLoad?: (totalPages: number) => void;
  children?: (props: {
    currentPage: number;
    totalPages: number;
    scale: number;
    pageRefs: React.RefObject<HTMLDivElement>[];
  }) => React.ReactNode;
}

const SCALE_STEP = 0.25;
const MIN_SCALE = 0.5;
const MAX_SCALE = 3.0;
const DEFAULT_SCALE = 1.0;

export function PDFViewerCore({
  documentUrl,
  file,
  className = '',
  initialPage = 1,
  initialScale = DEFAULT_SCALE,
  showControls = true,
  onStateChange,
  onPageChange,
  onScaleChange,
  onDocumentLoad,
  children
}: PDFViewerCoreProps) {
  const [state, setState] = useState<PDFViewerState>({
    currentPage: initialPage,
    totalPages: 0,
    scale: initialScale,
    isLoading: true,
    error: null
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const pageRefs = useRef<React.RefObject<HTMLDivElement>[]>([]);

  // Update page refs when total pages changes
  useEffect(() => {
    pageRefs.current = Array(state.totalPages)
      .fill(0)
      .map((_, i) => pageRefs.current[i] || React.createRef<HTMLDivElement>());
  }, [state.totalPages]);

  // Notify parent of state changes
  useEffect(() => {
    onStateChange?.(state);
  }, [state, onStateChange]);

  const updateState = useCallback((updates: Partial<PDFViewerState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const onDocumentLoadSuccess = useCallback((pdf: { numPages: number }) => {
    const totalPages = pdf.numPages;
    updateState({
      totalPages,
      isLoading: false,
      error: null
    });
    onDocumentLoad?.(totalPages);
  }, [updateState, onDocumentLoad]);

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('PDF load error:', error);
    updateState({
      isLoading: false,
      error: `Failed to load PDF: ${error.message}`
    });
  }, [updateState]);

  const onPageLoadSuccess = useCallback((page: { pageNumber: number }) => {
    // Page loaded successfully - could add per-page loading states here
  }, []);

  const onPageLoadError = useCallback((error: Error) => {
    console.error('Page load error:', error);
    // Handle individual page load errors
  }, []);

  // Viewer controls
  const controls: ViewerControls = useMemo(() => ({
    zoomIn: () => {
      const newScale = Math.min(MAX_SCALE, state.scale + SCALE_STEP);
      updateState({ scale: newScale });
      onScaleChange?.(newScale);
    },

    zoomOut: () => {
      const newScale = Math.max(MIN_SCALE, state.scale - SCALE_STEP);
      updateState({ scale: newScale });
      onScaleChange?.(newScale);
    },

    resetZoom: () => {
      updateState({ scale: DEFAULT_SCALE });
      onScaleChange?.(DEFAULT_SCALE);
    },

    goToPage: (page: number) => {
      const clampedPage = Math.max(1, Math.min(state.totalPages, page));
      updateState({ currentPage: clampedPage });
      onPageChange?.(clampedPage);

      // Scroll to page
      const pageRef = pageRefs.current[clampedPage - 1];
      if (pageRef?.current) {
        pageRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    },

    nextPage: () => {
      if (state.currentPage < state.totalPages) {
        const newPage = state.currentPage + 1;
        const clampedPage = Math.max(1, Math.min(state.totalPages, newPage));
        updateState({ currentPage: clampedPage });
        onPageChange?.(clampedPage);

        // Scroll to page
        const pageRef = pageRefs.current[clampedPage - 1];
        if (pageRef?.current) {
          pageRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    },

    previousPage: () => {
      if (state.currentPage > 1) {
        const newPage = state.currentPage - 1;
        const clampedPage = Math.max(1, Math.min(state.totalPages, newPage));
        updateState({ currentPage: clampedPage });
        onPageChange?.(clampedPage);

        // Scroll to page
        const pageRef = pageRefs.current[clampedPage - 1];
        if (pageRef?.current) {
          pageRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    }
  }), [state.scale, state.currentPage, state.totalPages, updateState, onScaleChange, onPageChange]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!containerRef.current?.contains(event.target as Node)) return;

      switch (event.key) {
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault();
          controls.previousPage();
          break;
        case 'ArrowDown':
        case 'PageDown':
          event.preventDefault();
          controls.nextPage();
          break;
        case '+':
        case '=':
          event.preventDefault();
          controls.zoomIn();
          break;
        case '-':
          event.preventDefault();
          controls.zoomOut();
          break;
        case '0':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            controls.resetZoom();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [controls]);

  const renderError = () => (
    <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      <div className="text-center">
        <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load PDF</h3>
        <p className="text-sm text-gray-600 mb-4">
          {state.error || 'An unexpected error occurred while loading the document.'}
        </p>
        <Button 
          variant="outline" 
          onClick={() => {
            updateState({ isLoading: true, error: null });
            // Trigger reload by forcing re-render
          }}
        >
          Try Again
        </Button>
      </div>
    </div>
  );

  const renderLoading = () => (
    <div className="flex items-center justify-center h-96">
      <div className="text-center">
        <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin text-blue-600" />
        <p className="text-sm text-gray-600">Loading PDF document...</p>
      </div>
    </div>
  );

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
        {/* Page Navigation */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={controls.previousPage}
            disabled={state.currentPage <= 1}
            className="h-8"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <input
              type="number"
              min={1}
              max={state.totalPages}
              value={state.currentPage}
              onChange={(e) => controls.goToPage(Number.parseInt(e.target.value) || 1)}
              className="w-16 px-2 py-1 text-center border border-gray-300 rounded text-sm"
            />
            <span>of {state.totalPages}</span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={controls.nextPage}
            disabled={state.currentPage >= state.totalPages}
            className="h-8"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={controls.zoomOut}
            disabled={state.scale <= MIN_SCALE}
            className="h-8"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span className="min-w-[4rem] text-center">
              {Math.round(state.scale * 100)}%
            </span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={controls.zoomIn}
            disabled={state.scale >= MAX_SCALE}
            className="h-8"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={controls.resetZoom}
            className="h-8"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
        </div>
      </div>
    );
  };

  const renderPages = () => {
    if (state.totalPages === 0) return null;

    return (
      <div className="space-y-6 p-4">
        {Array.from(new Array(state.totalPages), (_, index) => {
          const pageNumber = index + 1;
          return (
            <div
              key={pageNumber}
              ref={pageRefs.current[index]}
              className="flex justify-center"
              data-page-number={pageNumber}
            >
              <div className="relative border border-gray-300 shadow-lg bg-white">
                <Page
                  pageNumber={pageNumber}
                  scale={state.scale}
                  onLoadSuccess={onPageLoadSuccess}
                  onLoadError={onPageLoadError}
                  renderTextLayer={false}
                  renderAnnotationLayer={false}
                  className="max-w-full"
                />
                
                {/* Page number indicator */}
                <div className="absolute top-2 left-2 bg-black/75 text-white px-2 py-1 rounded text-xs z-10">
                  Page {pageNumber}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (state.error) {
    return <div className={className}>{renderError()}</div>;
  }

  return (
    <div 
      ref={containerRef}
      className={`pdf-viewer flex flex-col h-full ${className}`}

    >
      {renderControls()}
      
      <div className="flex-1 overflow-auto bg-gray-100" style={{ scrollBehavior: 'smooth' }}>
        <Document
          file={documentUrl || file}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={renderLoading()}
          error={renderError()}
          className="h-full"
        >
          {renderPages()}
        </Document>
      </div>
      
      {/* Render children with context */}
      {children?.({
        currentPage: state.currentPage,
        totalPages: state.totalPages,
        scale: state.scale,
        pageRefs: pageRefs.current
      })}
    </div>
  );
}
