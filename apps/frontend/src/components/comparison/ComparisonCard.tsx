interface BoundingBox {
  x: number
  y: number
  width: number
  height: number
  page?: number
}

interface FindingData {
  id: string
  findingType: 'document_layout_embedding' | 'layout_ocr_comparison' | 'llm_service_price_extraction'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  summary: string
  evidence: {
    chunkId1: string
    chunkId2: string
    score: number
    highlightDetails: {
      text1: string
      text2: string
    }
    boundingBoxes?: {
      doc1: BoundingBox[]
      doc2: BoundingBox[]
    }
    extractedServices?: {
      doc1: Array<{ service: string; price: number; suspicious: boolean }>
      doc2: Array<{ service: string; price: number; suspicious: boolean }>
    }
  }
}

interface ComparisonCardProps {
  finding: FindingData
  isSelected: boolean
  isAddedToReport: boolean
  onClick: () => void
  onAddToReport: () => void
}

export function ComparisonCard({ 
  finding, 
  isSelected, 
  isAddedToReport, 
  onClick, 
  onAddToReport 
}: ComparisonCardProps) {
  const getRiskColor = (level: FindingData['riskLevel']) => {
    switch (level) {
      case 'critical':
        return 'border-red-500 bg-red-50'
      case 'high':
        return 'border-orange-500 bg-orange-50'
      case 'medium':
        return 'border-yellow-500 bg-yellow-50'
      case 'low':
        return 'border-blue-500 bg-blue-50'
    }
  }

  const getRiskBadgeColor = (level: FindingData['riskLevel']) => {
    switch (level) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const getAlgorithmName = (type: FindingData['findingType']) => {
    switch (type) {
      case 'document_layout_embedding':
        return 'Document Layout + Vector Similarity'
      case 'layout_ocr_comparison':
        return 'Layout OCR Boundary Comparison'
      case 'llm_service_price_extraction':
        return 'LLM Service & Price Analysis'
    }
  }

  const getAlgorithmIcon = (type: FindingData['findingType']) => {
    switch (type) {
      case 'document_layout_embedding':
        return '📋'
      case 'layout_ocr_comparison':
        return '📐'
      case 'llm_service_price_extraction':
        return '🔍'
    }
  }

  return (
    <button 
      type="button"
      className={`
        w-full text-left rounded-lg border-2 transition-all duration-200 cursor-pointer
        ${getRiskColor(finding.riskLevel)}
        ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2 shadow-lg' : 'hover:shadow-md'}
      `}
      onClick={onClick}
    >
      {/* Header */}
      <div className="p-4 pb-3 border-b border-opacity-20 border-slate-400">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{getAlgorithmIcon(finding.findingType)}</div>
            <div>
              <h3 className="font-semibold text-slate-900 text-sm">
                {getAlgorithmName(finding.findingType)}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getRiskBadgeColor(finding.riskLevel)}`}>
                  {finding.riskLevel.toUpperCase()}
                </span>
                <span className="text-xs text-slate-600">
                  {Math.round(finding.evidence.score * 100)}% similarity
                </span>
              </div>
            </div>
          </div>
          
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation()
              onAddToReport()
            }}
            className={`
              px-3 py-2 rounded-lg text-xs font-medium transition-colors
              ${isAddedToReport 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : 'bg-white text-slate-600 border border-slate-300 hover:bg-slate-50'
              }
            `}
          >
            {isAddedToReport ? '✓ Added' : '+ Add to Report'}
          </button>
        </div>
      </div>

      {/* Summary */}
      <div className="p-4 pb-3">
        <p className="text-sm text-slate-800 leading-relaxed">
          {finding.summary}
        </p>
      </div>

      {/* Evidence Preview */}
      <div className="px-4 pb-4">
        {finding.findingType === 'layout_ocr_comparison' && finding.evidence.boundingBoxes ? (
          // Boundary Box Visualization
          <div className="space-y-4">
            <div className="text-xs text-slate-600 font-medium">Boundary Box Comparison</div>
            <div className="grid grid-cols-2 gap-4">
              {/* Document 1 Boundaries */}
              <div className="space-y-2">
                <div className="flex items-center text-xs text-slate-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                  Document 1 Layout
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="relative bg-white border border-slate-200 rounded" style={{height: '120px', width: '100%'}}>
                    {finding.evidence.boundingBoxes.doc1.slice(0, 4).map((box, index) => (
                      <div
                        key={index}
                        className="absolute border-2 border-blue-500 bg-blue-100 opacity-60 rounded"
                        style={{
                          left: `${(box.x / 500) * 100}%`,
                          top: `${(box.y / 600) * 100}%`,
                          width: `${(box.width / 500) * 100}%`,
                          height: `${(box.height / 600) * 100}%`
                        }}
                        title={`Page ${box.page || 1}: (${box.x}, ${box.y}) ${box.width}×${box.height}`}
                      />
                    ))}
                  </div>
                  <div className="text-xs text-slate-600 mt-2">
                    {finding.evidence.boundingBoxes.doc1.length} elements detected
                  </div>
                </div>
              </div>

              {/* Document 2 Boundaries */}
              <div className="space-y-2">
                <div className="flex items-center text-xs text-slate-600">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-2" />
                  Document 2 Layout
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                  <div className="relative bg-white border border-slate-200 rounded" style={{height: '120px', width: '100%'}}>
                    {finding.evidence.boundingBoxes.doc2.slice(0, 4).map((box, index) => (
                      <div
                        key={index}
                        className="absolute border-2 border-orange-500 bg-orange-100 opacity-60 rounded"
                        style={{
                          left: `${(box.x / 500) * 100}%`,
                          top: `${(box.y / 600) * 100}%`,
                          width: `${(box.width / 500) * 100}%`,
                          height: `${(box.height / 600) * 100}%`
                        }}
                        title={`Page ${box.page || 1}: (${box.x}, ${box.y}) ${box.width}×${box.height}`}
                      />
                    ))}
                  </div>
                  <div className="text-xs text-slate-600 mt-2">
                    {finding.evidence.boundingBoxes.doc2.length} elements detected
                  </div>
                </div>
              </div>
            </div>
            
            {/* Text Preview for OCR */}
            <div className="grid grid-cols-2 gap-4 pt-2 border-t border-slate-200">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-xs text-slate-800 font-mono leading-tight line-clamp-3">
                  {finding.evidence.highlightDetails.text1}
                </div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="text-xs text-slate-800 font-mono leading-tight line-clamp-3">
                  {finding.evidence.highlightDetails.text2}
                </div>
              </div>
            </div>
          </div>
        ) : finding.findingType === 'llm_service_price_extraction' && finding.evidence.extractedServices ? (
          // Service Extraction Visualization
          <div className="space-y-4">
            <div className="text-xs text-slate-600 font-medium">Extracted Services & Pricing</div>
            <div className="grid grid-cols-2 gap-4">
              {/* Document 1 Services */}
              <div className="space-y-2">
                <div className="flex items-center text-xs text-slate-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                  Document 1 Services
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 space-y-2">
                  {finding.evidence.extractedServices.doc1.map((service, index) => (
                    <div key={index} className="flex items-center justify-between text-xs">
                      <span className={`${service.suspicious ? 'text-red-700 font-medium' : 'text-slate-700'}`}>
                        {service.service}
                      </span>
                      <div className="flex items-center space-x-1">
                        <span className="font-mono text-slate-800">
                          {service.price < 1 ? `${(service.price * 100).toFixed(1)}%` : `$${service.price.toLocaleString()}`}
                        </span>
                        {service.suspicious && <span className="text-red-500">⚠</span>}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Document 2 Services */}
              <div className="space-y-2">
                <div className="flex items-center text-xs text-slate-600">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-2" />
                  Document 2 Services
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 space-y-2">
                  {finding.evidence.extractedServices.doc2.map((service, index) => (
                    <div key={index} className="flex items-center justify-between text-xs">
                      <span className={`${service.suspicious ? 'text-red-700 font-medium' : 'text-slate-700'}`}>
                        {service.service}
                      </span>
                      <div className="flex items-center space-x-1">
                        <span className="font-mono text-slate-800">
                          {service.price < 1 ? `${(service.price * 100).toFixed(1)}%` : `$${service.price.toLocaleString()}`}
                        </span>
                        {service.suspicious && <span className="text-red-500">⚠</span>}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Text Preview for LLM */}
            <div className="grid grid-cols-2 gap-4 pt-2 border-t border-slate-200">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-xs text-slate-800 font-mono leading-tight line-clamp-3">
                  {finding.evidence.highlightDetails.text1}
                </div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="text-xs text-slate-800 font-mono leading-tight line-clamp-3">
                  {finding.evidence.highlightDetails.text2}
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Default Text Evidence (for document_layout_embedding)
          <div className="grid grid-cols-2 gap-4">
            {/* Document 1 Evidence */}
            <div className="space-y-2">
              <div className="flex items-center text-xs text-slate-600">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2" />
                Document 1
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-xs text-slate-800 font-mono leading-tight line-clamp-4">
                  {finding.evidence.highlightDetails.text1}
                </div>
              </div>
            </div>

            {/* Document 2 Evidence */}
            <div className="space-y-2">
              <div className="flex items-center text-xs text-slate-600">
                <div className="w-2 h-2 bg-orange-500 rounded-full mr-2" />
                Document 2
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="text-xs text-slate-800 font-mono leading-tight line-clamp-4">
                  {finding.evidence.highlightDetails.text2}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer Actions */}
      <div className="px-4 pb-4 pt-2 border-t border-opacity-20 border-slate-400">
        <div className="flex items-center justify-between text-xs text-slate-600">
          <span>Click to view in document viewer</span>
          <div className="flex items-center space-x-2">
            <span>Score: {Math.round(finding.evidence.score * 100)}%</span>
            {isSelected && (
              <span className="text-blue-600 font-medium">← Selected</span>
            )}
          </div>
        </div>
      </div>
    </button>
  )
}

// Add Tailwind CSS utility for line clamping
const style = document.createElement('style')
style.textContent = `
  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`
if (typeof document !== 'undefined') {
  document.head.appendChild(style)
}