// Side-by-side document viewer for comparison

import { useState, useCallback, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link, Unlink, ArrowLeftRight, Maximize2 } from 'lucide-react';
import { Button } from '../ui/button';
import { EnhancedDocumentViewer } from '../pdf/EnhancedDocumentViewer';
import { documentService } from '../../services/documentService';
import type { DocumentComparisonResult } from '../../types/document';

interface SideBySideDocumentViewerProps {
  document1Id: string;
  document2Id: string;
  comparisonResult?: DocumentComparisonResult;
  onChunkCompare?: (chunkId1: string, chunkId2: string) => void;
  onSelectionChange?: (selectedChunks: { doc1: string[]; doc2: string[] }) => void;
  className?: string;
}

interface ViewerSyncState {
  isScrollSynced: boolean;
  isZoomSynced: boolean;
  currentPage: number;
  scale: number;
}

export function SideBySideDocumentViewer({
  document1Id,
  document2Id,
  comparisonResult,
  onChunkCompare,
  onSelectionChange,
  className = ''
}: SideBySideDocumentViewerProps) {
  const [syncState, setSyncState] = useState<ViewerSyncState>({
    isScrollSynced: true,
    isZoomSynced: true,
    currentPage: 1,
    scale: 1.0
  });

  const [selectedChunks, setSelectedChunks] = useState<{
    doc1: string[];
    doc2: string[];
  }>({
    doc1: [],
    doc2: []
  });

  const [fullscreenDoc, setFullscreenDoc] = useState<'doc1' | 'doc2' | null>(null);

  // Fetch comparison result if not provided
  const { data: fetchedComparisonResult, isLoading: comparisonLoading } = useQuery({
    queryKey: ['document-comparison', document1Id, document2Id],
    queryFn: () => documentService.analyzeSemanticSimilarity(document1Id, document2Id, 0.7),
    enabled: !comparisonResult && !!document1Id && !!document2Id
  });

  const activeComparisonResult = comparisonResult || fetchedComparisonResult;

  // Handle chunk selection for comparison
  const handleChunkSelection = useCallback((docType: 'doc1' | 'doc2', chunkIds: string[]) => {
    const newSelection = {
      ...selectedChunks,
      [docType]: chunkIds
    };
    
    setSelectedChunks(newSelection);
    onSelectionChange?.(newSelection);

    // Auto-trigger comparison if both documents have selections
    if (docType === 'doc1' && newSelection.doc2.length > 0) {
      chunkIds.forEach(chunkId1 => {
        newSelection.doc2.forEach(chunkId2 => {
          onChunkCompare?.(chunkId1, chunkId2);
        });
      });
    } else if (docType === 'doc2' && newSelection.doc1.length > 0) {
      chunkIds.forEach(chunkId2 => {
        newSelection.doc1.forEach(chunkId1 => {
          onChunkCompare?.(chunkId1, chunkId2);
        });
      });
    }
  }, [selectedChunks, onChunkCompare, onSelectionChange]);

  // Handle individual chunk comparison
  const handleIndividualChunkCompare = useCallback((chunkId: string) => {
    // Determine which document this chunk belongs to and find similar chunks in the other
    if (activeComparisonResult) {
      const similarChunks = activeComparisonResult.similar_chunks.filter(
        pair => pair.sourceChunk.id === chunkId || pair.targetChunk.id === chunkId
      );
      
      similarChunks.forEach(pair => {
        onChunkCompare?.(pair.sourceChunk.id, pair.targetChunk.id);
      });
    }
  }, [activeComparisonResult, onChunkCompare]);

  // Toggle sync options
  const toggleScrollSync = useCallback(() => {
    setSyncState(prev => ({
      ...prev,
      isScrollSynced: !prev.isScrollSynced
    }));
  }, []);

  const toggleZoomSync = useCallback(() => {
    setSyncState(prev => ({
      ...prev,
      isZoomSynced: !prev.isZoomSynced
    }));
  }, []);

  const toggleFullscreen = useCallback((docType: 'doc1' | 'doc2') => {
    setFullscreenDoc(prev => prev === docType ? null : docType);
  }, []);

  // Create filtered comparison results for each document
  const doc1ComparisonResult = useMemo(() => {
    if (!activeComparisonResult) return undefined;
    
    return {
      ...activeComparisonResult,
      similar_chunks: activeComparisonResult.similar_chunks.filter(pair => 
        // Include chunks that belong to document 1
        pair.sourceChunk.id // Assuming sourceChunk is from document 1
      )
    };
  }, [activeComparisonResult]);

  const doc2ComparisonResult = useMemo(() => {
    if (!activeComparisonResult) return undefined;
    
    return {
      ...activeComparisonResult,
      similar_chunks: activeComparisonResult.similar_chunks.map(pair => ({
        ...pair,
        // Swap source and target for document 2's perspective
        sourceChunk: pair.targetChunk,
        targetChunk: pair.sourceChunk
      })).filter(pair => 
        pair.sourceChunk.id // Now sourceChunk represents document 2 chunks
      )
    };
  }, [activeComparisonResult]);

  // Calculate comparison statistics
  const comparisonStats = useMemo(() => {
    if (!activeComparisonResult) return null;

    const { similar_chunks, overall_similarity } = activeComparisonResult;
    const criticalMatches = similar_chunks.filter(pair => pair.similarity >= 0.9).length;
    const highMatches = similar_chunks.filter(pair => pair.similarity >= 0.7 && pair.similarity < 0.9).length;
    const mediumMatches = similar_chunks.filter(pair => pair.similarity >= 0.5 && pair.similarity < 0.7).length;

    return {
      totalMatches: similar_chunks.length,
      criticalMatches,
      highMatches,
      mediumMatches,
      averageSimilarity: overall_similarity.average_score,
      maxSimilarity: overall_similarity.max_score,
      riskLevel: overall_similarity.risk_assessment
    };
  }, [activeComparisonResult]);

  // Render comparison header
  const renderComparisonHeader = () => (
    <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50">
      {/* Comparison info */}
      <div className="flex items-center space-x-4">
        <div className="text-sm font-semibold text-gray-900">
          Document Comparison
        </div>
        
        {comparisonStats && (
          <div className="flex items-center space-x-4 text-xs text-gray-600">
            <span>{comparisonStats.totalMatches} similar chunks</span>
            <span>
              Avg: {Math.round(comparisonStats.averageSimilarity * 100)}%
            </span>
            <span
              className={`px-2 py-1 rounded ${
                comparisonStats.riskLevel === 'high'
                  ? 'bg-red-100 text-red-700'
                  : comparisonStats.riskLevel === 'medium'
                  ? 'bg-orange-100 text-orange-700'
                  : 'bg-green-100 text-green-700'
              }`}
            >
              {comparisonStats.riskLevel.toUpperCase()} RISK
            </span>
          </div>
        )}
      </div>

      {/* Sync controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={toggleScrollSync}
          className={`text-xs ${
            syncState.isScrollSynced
              ? 'bg-blue-50 border-blue-200 text-blue-700'
              : ''
          }`}
        >
          {syncState.isScrollSynced ? (
            <Link className="w-3 h-3 mr-1" />
          ) : (
            <Unlink className="w-3 h-3 mr-1" />
          )}
          Sync Scroll
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={toggleZoomSync}
          className={`text-xs ${
            syncState.isZoomSynced
              ? 'bg-blue-50 border-blue-200 text-blue-700'
              : ''
          }`}
        >
          Sync Zoom
        </Button>

        <div className="w-px h-4 bg-gray-300" />

        <Button
          variant="outline"
          size="sm"
          onClick={() => toggleFullscreen('doc1')}
          className="text-xs"
        >
          <Maximize2 className="w-3 h-3" />
        </Button>
      </div>
    </div>
  );

  // Render comparison statistics sidebar
  const renderStatsSidebar = () => (
    <div className="w-64 border-l border-gray-200 bg-gray-50 p-4 overflow-y-auto">
      <h3 className="text-sm font-semibold text-gray-900 mb-3">
        Comparison Statistics
      </h3>

      {comparisonStats ? (
        <div className="space-y-4">
          {/* Risk overview */}
          <div className="bg-white p-3 rounded border">
            <div className="text-xs font-medium text-gray-600 mb-2">Overall Risk</div>
            <div
              className={`text-lg font-bold ${
                comparisonStats.riskLevel === 'high'
                  ? 'text-red-600'
                  : comparisonStats.riskLevel === 'medium'
                  ? 'text-orange-600'
                  : 'text-green-600'
              }`}
            >
              {comparisonStats.riskLevel.toUpperCase()}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Max similarity: {Math.round(comparisonStats.maxSimilarity * 100)}%
            </div>
          </div>

          {/* Match breakdown */}
          <div className="bg-white p-3 rounded border">
            <div className="text-xs font-medium text-gray-600 mb-2">Similar Chunks</div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-red-600">Critical (≥90%)</span>
                <span className="text-sm font-semibold">{comparisonStats.criticalMatches}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-orange-600">High (70-89%)</span>
                <span className="text-sm font-semibold">{comparisonStats.highMatches}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-yellow-600">Medium (50-69%)</span>
                <span className="text-sm font-semibold">{comparisonStats.mediumMatches}</span>
              </div>
            </div>
          </div>

          {/* Selected chunks */}
          {(selectedChunks.doc1.length > 0 || selectedChunks.doc2.length > 0) && (
            <div className="bg-white p-3 rounded border">
              <div className="text-xs font-medium text-gray-600 mb-2">Selected Chunks</div>
              <div className="space-y-1">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-blue-600">Document 1</span>
                  <span className="text-sm font-semibold">{selectedChunks.doc1.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-orange-600">Document 2</span>
                  <span className="text-sm font-semibold">{selectedChunks.doc2.length}</span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-xs"
              onClick={() => setSelectedChunks({ doc1: [], doc2: [] })}
              disabled={selectedChunks.doc1.length === 0 && selectedChunks.doc2.length === 0}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      ) : (
        <div className="text-xs text-gray-500">
          Loading comparison statistics...
        </div>
      )}
    </div>
  );

  // Handle fullscreen mode
  if (fullscreenDoc) {
    const documentId = fullscreenDoc === 'doc1' ? document1Id : document2Id;
    const compResult = fullscreenDoc === 'doc1' ? doc1ComparisonResult : doc2ComparisonResult;
    
    return (
      <div className={`fullscreen-document-viewer h-full ${className}`}>
        <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-gray-50">
          <div className="text-sm font-semibold">
            {fullscreenDoc === 'doc1' ? activeComparisonResult?.document1_name : activeComparisonResult?.document2_name}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFullscreenDoc(null)}
            className="text-xs"
          >
            Exit Fullscreen
          </Button>
        </div>
        
        <EnhancedDocumentViewer
          documentId={documentId}
          comparisonResult={compResult}
          onChunkSelect={(chunkIds) => handleChunkSelection(fullscreenDoc, chunkIds)}
          onChunkCompare={handleIndividualChunkCompare}
          className="h-full"
        />
      </div>
    );
  }

  return (
    <div className={`side-by-side-viewer flex flex-col h-full ${className}`}>
      {/* Header */}
      {renderComparisonHeader()}

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Document viewers */}
        <div className="flex flex-1">
          {/* Document 1 */}
          <div className="flex-1 flex flex-col border-r border-gray-200">
            <div className="px-3 py-2 bg-blue-50 border-b border-blue-200 flex items-center justify-between">
              <div>
                <h3 className="text-sm font-semibold text-blue-900">
                  {activeComparisonResult?.document1_name || 'Document 1'}
                </h3>
                <p className="text-xs text-blue-700">
                  {activeComparisonResult?.company1_name}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleFullscreen('doc1')}
                className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
              >
                <Maximize2 className="w-3 h-3" />
              </Button>
            </div>
            
            <EnhancedDocumentViewer
              documentId={document1Id}
              comparisonResult={doc1ComparisonResult}
              showControls={!syncState.isScrollSynced && !syncState.isZoomSynced}
              onChunkSelect={(chunkIds) => handleChunkSelection('doc1', chunkIds)}
              onChunkCompare={handleIndividualChunkCompare}
              className="flex-1"
            />
          </div>

          {/* Document 2 */}
          <div className="flex-1 flex flex-col">
            <div className="px-3 py-2 bg-orange-50 border-b border-orange-200 flex items-center justify-between">
              <div>
                <h3 className="text-sm font-semibold text-orange-900">
                  {activeComparisonResult?.document2_name || 'Document 2'}
                </h3>
                <p className="text-xs text-orange-700">
                  {activeComparisonResult?.company2_name}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleFullscreen('doc2')}
                className="h-6 w-6 p-0 text-orange-600 hover:text-orange-800"
              >
                <Maximize2 className="w-3 h-3" />
              </Button>
            </div>
            
            <EnhancedDocumentViewer
              documentId={document2Id}
              comparisonResult={doc2ComparisonResult}
              showControls={!syncState.isScrollSynced && !syncState.isZoomSynced}
              onChunkSelect={(chunkIds) => handleChunkSelection('doc2', chunkIds)}
              onChunkCompare={handleIndividualChunkCompare}
              className="flex-1"
            />
          </div>
        </div>

        {/* Statistics sidebar */}
        {renderStatsSidebar()}
      </div>
    </div>
  );
}