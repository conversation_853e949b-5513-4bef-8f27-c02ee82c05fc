// Coordinate mapping utility for converting between PDF and display coordinate systems

import type { BoundingBox, Rectangle } from '../types/document';
import type React from 'react';

export class CoordinateMapper {
  /**
   * Convert PDF coordinates to display coordinates
   * 
   * PDF coordinate system:
   * - Origin: Bottom-left corner (0,0)
   * - Y-axis: Upward direction
   * - Units: Points (1/72 inch)
   * 
   * Display coordinate system:
   * - Origin: Top-left corner (0,0)
   * - Y-axis: Downward direction
   * - Units: Pixels
   * 
   * @param pdfBox - Bounding box in PDF coordinates
   * @param pageWidth - PDF page width in points
   * @param pageHeight - PDF page height in points
   * @param scale - Current display scale factor
   * @returns Rectangle in display coordinates
   */
  static pdfToDisplay(
    pdfBox: BoundingBox,
    pageWidth: number,
    pageHeight: number,
    scale = 1.0
  ): Rectangle {
    // Convert PDF coordinates to display coordinates
    // PDF Y-axis is inverted (bottom-left origin vs top-left origin)
    const displayX = pdfBox.x1 * scale;
    const displayY = (pageHeight - pdfBox.y2) * scale; // Flip Y-axis
    const displayWidth = (pdfBox.x2 - pdfBox.x1) * scale;
    const displayHeight = (pdfBox.y2 - pdfBox.y1) * scale;

    return {
      x: Math.round(displayX),
      y: Math.round(displayY),
      width: Math.round(displayWidth),
      height: Math.round(displayHeight)
    };
  }

  /**
   * Convert display coordinates back to PDF coordinates
   * 
   * @param displayRect - Rectangle in display coordinates
   * @param pageWidth - PDF page width in points
   * @param pageHeight - PDF page height in points
   * @param scale - Current display scale factor
   * @returns BoundingBox in PDF coordinates
   */
  static displayToPdf(
    displayRect: Rectangle,
    pageWidth: number,
    pageHeight: number,
    scale = 1.0
  ): BoundingBox {
    // Convert display coordinates back to PDF coordinates
    const pdfX1 = displayRect.x / scale;
    const pdfX2 = (displayRect.x + displayRect.width) / scale;
    
    // Flip Y-axis back to PDF coordinate system
    const pdfY2 = pageHeight - (displayRect.y / scale);
    const pdfY1 = pageHeight - ((displayRect.y + displayRect.height) / scale);

    return {
      x1: Math.round(pdfX1 * 100) / 100, // Round to 2 decimal places
      y1: Math.round(pdfY1 * 100) / 100,
      x2: Math.round(pdfX2 * 100) / 100,
      y2: Math.round(pdfY2 * 100) / 100,
      width: Math.round((pdfX2 - pdfX1) * 100) / 100,
      height: Math.round((pdfY2 - pdfY1) * 100) / 100
    };
  }

  /**
   * Calculate overlay CSS style properties for a chunk
   * 
   * @param chunk - Document chunk with bounding box
   * @param pageRect - Current page DOM rectangle
   * @param scale - Current display scale
   * @returns CSS style properties for positioning the overlay
   */
  static calculateOverlayStyle(
    chunk: { bounding_box: BoundingBox },
    pageRect: { width: number; height: number },
    scale = 1.0
  ): React.CSSProperties {
    const displayCoords = this.pdfToDisplay(
      chunk.bounding_box,
      pageRect.width / scale, // Convert display size back to PDF size
      pageRect.height / scale,
      scale
    );

    return {
      position: 'absolute',
      left: `${displayCoords.x}px`,
      top: `${displayCoords.y}px`,
      width: `${displayCoords.width}px`,
      height: `${displayCoords.height}px`,
      pointerEvents: 'all',
      zIndex: 10,
      boxSizing: 'border-box'
    };
  }

  /**
   * Check if a point (in display coordinates) is within a chunk's bounding box
   * 
   * @param point - Point in display coordinates
   * @param chunk - Document chunk
   * @param pageRect - Current page DOM rectangle
   * @param scale - Current display scale
   * @returns True if point is within the chunk's bounds
   */
  static isPointInChunk(
    point: { x: number; y: number },
    chunk: { bounding_box: BoundingBox },
    pageRect: { width: number; height: number },
    scale = 1.0
  ): boolean {
    const displayCoords = this.pdfToDisplay(
      chunk.bounding_box,
      pageRect.width / scale,
      pageRect.height / scale,
      scale
    );

    return (
      point.x >= displayCoords.x &&
      point.x <= displayCoords.x + displayCoords.width &&
      point.y >= displayCoords.y &&
      point.y <= displayCoords.y + displayCoords.height
    );
  }

  /**
   * Calculate the center point of a chunk in display coordinates
   * 
   * @param chunk - Document chunk
   * @param pageRect - Current page DOM rectangle
   * @param scale - Current display scale
   * @returns Center point in display coordinates
   */
  static getChunkCenter(
    chunk: { bounding_box: BoundingBox },
    pageRect: { width: number; height: number },
    scale = 1.0
  ): { x: number; y: number } {
    const displayCoords = this.pdfToDisplay(
      chunk.bounding_box,
      pageRect.width / scale,
      pageRect.height / scale,
      scale
    );

    return {
      x: displayCoords.x + displayCoords.width / 2,
      y: displayCoords.y + displayCoords.height / 2
    };
  }

  /**
   * Check if two chunks overlap in display coordinates
   * 
   * @param chunk1 - First chunk
   * @param chunk2 - Second chunk
   * @param pageRect - Current page DOM rectangle
   * @param scale - Current display scale
   * @returns True if chunks overlap
   */
  static doChunksOverlap(
    chunk1: { bounding_box: BoundingBox },
    chunk2: { bounding_box: BoundingBox },
    pageRect: { width: number; height: number },
    scale = 1.0
  ): boolean {
    const coords1 = this.pdfToDisplay(chunk1.bounding_box, pageRect.width / scale, pageRect.height / scale, scale);
    const coords2 = this.pdfToDisplay(chunk2.bounding_box, pageRect.width / scale, pageRect.height / scale, scale);

    return !(
      coords1.x + coords1.width < coords2.x ||
      coords2.x + coords2.width < coords1.x ||
      coords1.y + coords1.height < coords2.y ||
      coords2.y + coords2.height < coords1.y
    );
  }

  /**
   * Calculate optimal tooltip position to avoid going off-screen
   * 
   * @param chunk - Document chunk
   * @param pageRect - Current page DOM rectangle
   * @param scale - Current display scale
   * @param tooltipSize - Tooltip dimensions
   * @param containerRect - Container bounds
   * @returns Optimal tooltip position
   */
  static calculateTooltipPosition(
    chunk: { bounding_box: BoundingBox },
    pageRect: { width: number; height: number },
    scale = 1.0,
    tooltipSize: { width: number; height: number },
    containerRect: { width: number; height: number }
  ): { x: number; y: number; placement: 'top' | 'bottom' | 'left' | 'right' } {
    const chunkCenter = this.getChunkCenter(chunk, pageRect, scale);
    const displayCoords = this.pdfToDisplay(
      chunk.bounding_box,
      pageRect.width / scale,
      pageRect.height / scale,
      scale
    );

    // Try positions in order of preference: top, bottom, right, left
    const positions = [
      {
        placement: 'top' as const,
        x: chunkCenter.x - tooltipSize.width / 2,
        y: displayCoords.y - tooltipSize.height - 8
      },
      {
        placement: 'bottom' as const,
        x: chunkCenter.x - tooltipSize.width / 2,
        y: displayCoords.y + displayCoords.height + 8
      },
      {
        placement: 'right' as const,
        x: displayCoords.x + displayCoords.width + 8,
        y: chunkCenter.y - tooltipSize.height / 2
      },
      {
        placement: 'left' as const,
        x: displayCoords.x - tooltipSize.width - 8,
        y: chunkCenter.y - tooltipSize.height / 2
      }
    ];

    // Find the first position that fits within the container
    for (const pos of positions) {
      if (
        pos.x >= 0 &&
        pos.y >= 0 &&
        pos.x + tooltipSize.width <= containerRect.width &&
        pos.y + tooltipSize.height <= containerRect.height
      ) {
        return pos;
      }
    }

    // If no position fits perfectly, use the top position and clamp to bounds
    const fallback = positions[0];
    return {
      placement: fallback.placement,
      x: Math.max(0, Math.min(containerRect.width - tooltipSize.width, fallback.x)),
      y: Math.max(0, Math.min(containerRect.height - tooltipSize.height, fallback.y))
    };
  }

  /**
   * Normalize bounding box to ensure x1 <= x2 and y1 <= y2
   * 
   * @param bbox - Potentially unnormalized bounding box
   * @returns Normalized bounding box
   */
  static normalizeBoundingBox(bbox: BoundingBox): BoundingBox {
    const x1 = Math.min(bbox.x1, bbox.x2);
    const x2 = Math.max(bbox.x1, bbox.x2);
    const y1 = Math.min(bbox.y1, bbox.y2);
    const y2 = Math.max(bbox.y1, bbox.y2);

    return {
      x1,
      y1,
      x2,
      y2,
      width: x2 - x1,
      height: y2 - y1
    };
  }
}
